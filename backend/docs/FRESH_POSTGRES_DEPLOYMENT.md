# 🆕 Fresh PostgreSQL Database Deployment Guide

## Prerequisites

### 1. PostgreSQL Installation & Configuration

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install postgresql postgresql-contrib

# CentOS/RHEL
sudo yum install postgresql-server postgresql-contrib
sudo postgresql-setup initdb

# macOS (Homebrew)
brew install postgresql
brew services start postgresql
```

### 2. Required Extensions Installation

```bash
# Connect as postgres superuser
sudo -u postgres psql

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "unaccent";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- Configure pg_stat_statements
ALTER SYSTEM SET shared_preload_libraries = 'pg_stat_statements';
ALTER SYSTEM SET pg_stat_statements.track = 'all';
ALTER SYSTEM SET pg_stat_statements.max = 10000;

-- Restart PostgreSQL to load extensions
sudo systemctl restart postgresql
```

### 3. Database and User Setup

```bash
# Create database and user
sudo -u postgres psql << 'EOF'
-- Create database
CREATE DATABASE dbcsrit;

-- Create user
CREATE USER aistech WITH PASSWORD 'your_secure_password';

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE dbcsrit TO aistech;

-- Connect to the database
\c dbcsrit

-- Grant schema privileges
GRANT ALL ON SCHEMA public TO aistech;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO aistech;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO aistech;

-- Enable extensions in the database
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "unaccent";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";
EOF
```

### 4. PostgreSQL Configuration Optimization

```bash
# Edit postgresql.conf
sudo nano /etc/postgresql/*/main/postgresql.conf

# Add/modify these settings:
```

```ini
# Memory Settings
shared_buffers = 256MB                    # 25% of RAM for small systems
effective_cache_size = 1GB                # 75% of RAM
work_mem = 64MB                          # Per connection working memory
maintenance_work_mem = 256MB              # For maintenance operations

# Connection Settings
max_connections = 100                     # Adjust based on your needs
listen_addresses = '*'                    # Allow connections from all IPs

# Performance Settings
random_page_cost = 1.1                    # SSD optimization
effective_io_concurrency = 200            # SSD optimization
checkpoint_completion_target = 0.9
wal_buffers = 16MB

# Logging Settings
log_destination = 'stderr'
logging_collector = on
log_directory = 'log'
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_min_duration_statement = 1000         # Log slow queries
log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '

# Extensions
shared_preload_libraries = 'pg_stat_statements'
```

```bash
# Edit pg_hba.conf for authentication
sudo nano /etc/postgresql/*/main/pg_hba.conf

# Add this line for local development (adjust for production)
host    dbcsrit         aistech         0.0.0.0/0               md5

# Restart PostgreSQL
sudo systemctl restart postgresql
sudo systemctl enable postgresql
```

## Deployment Steps

### Step 1: Environment Configuration

```bash
# Create .env file
cat > backend/.env << 'EOF'
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USER=aistech
DB_PASS=your_secure_password
DB_NAME=dbcsrit

# Performance Configuration
DB_POOL_MIN=5
DB_POOL_MAX=20
DB_QUERY_TIMEOUT=30000
DB_CONNECTION_TIMEOUT=5000

# Security Configuration
DB_SSL=false
DB_SSL_REJECT_UNAUTHORIZED=false

# Cache Configuration (optional)
REDIS_HOST=localhost
REDIS_PORT=6379
CACHE_TTL=300

# Application Configuration
NODE_ENV=production
JWT_ACCESS_TOKEN_SECRET=your_jwt_secret
JWT_ACCESS_TOKEN_EXPIRATION_TIME=15m
JWT_REFRESH_TOKEN_SECRET=your_refresh_secret
JWT_REFRESH_TOKEN_EXPIRATION_TIME=7d
JWT_ACCESS_COOKIE=true
EOF
```

### Step 2: Initial Schema Creation

```bash
# Navigate to backend directory
cd backend

# Install dependencies
npm install

# Build the application
npm run build

# Create initial schema (first time only)
npm run migration:run

# Seed initial data (if needed)
npm run seed
```

### Step 3: Deploy Optimizations

```bash
# Make scripts executable
chmod +x scripts/*.sh

# Run the optimization deployment
./scripts/production-migration.sh

# Verify deployment
./scripts/performance-benchmark.sh full
```

### Step 4: Application Startup

```bash
# Start the application
npm run start:prod

# Or with PM2 for production
npm install -g pm2
pm2 start dist/main.js --name "csrit-backend"
pm2 startup
pm2 save
```

## Verification Steps

### 1. Database Connection Test
```bash
psql -h localhost -p 5432 -U aistech -d dbcsrit -c "SELECT version();"
```

### 2. Index Verification
```bash
psql -h localhost -p 5432 -U aistech -d dbcsrit -c "
SELECT COUNT(*) as optimization_indexes 
FROM pg_indexes 
WHERE schemaname = 'public' 
    AND indexname LIKE 'idx_%';"
```

### 3. Performance Test
```bash
# Run performance benchmark
./scripts/performance-benchmark.sh content

# Check application health
curl -f http://localhost:3000/api/berita
```

## Troubleshooting

### Common Issues

#### 1. Permission Denied
```bash
# Fix ownership
sudo chown -R postgres:postgres /var/lib/postgresql/
sudo chmod 700 /var/lib/postgresql/*/main
```

#### 2. Connection Refused
```bash
# Check PostgreSQL status
sudo systemctl status postgresql

# Check listening ports
sudo netstat -tlnp | grep 5432

# Check logs
sudo tail -f /var/log/postgresql/postgresql-*.log
```

#### 3. Extension Not Found
```bash
# Install additional packages
sudo apt install postgresql-contrib-*

# Or for specific extensions
sudo apt install postgresql-*-pg-trgm
```

### Performance Tuning

#### 1. Memory Settings Calculation
```bash
# For a 4GB RAM system:
shared_buffers = 1GB          # 25% of RAM
effective_cache_size = 3GB    # 75% of RAM
work_mem = 64MB              # Total RAM / max_connections / 4

# For an 8GB RAM system:
shared_buffers = 2GB
effective_cache_size = 6GB
work_mem = 128MB
```

#### 2. Monitor Performance
```bash
# Check slow queries
psql -h localhost -p 5432 -U aistech -d dbcsrit -c "
SELECT query, mean_exec_time, calls 
FROM pg_stat_statements 
WHERE mean_exec_time > 1000 
ORDER BY mean_exec_time DESC 
LIMIT 10;"
```

## Security Hardening

### 1. Firewall Configuration
```bash
# Allow PostgreSQL port
sudo ufw allow 5432/tcp

# Restrict to specific IPs (recommended)
sudo ufw allow from ***********/24 to any port 5432
```

### 2. SSL Configuration (Production)
```bash
# Generate SSL certificates
sudo openssl req -new -x509 -days 365 -nodes -text \
    -out /etc/ssl/certs/server.crt \
    -keyout /etc/ssl/private/server.key \
    -subj "/CN=dbserver.yourdomain.com"

# Set permissions
sudo chmod 600 /etc/ssl/private/server.key
sudo chown postgres:postgres /etc/ssl/private/server.key
sudo chown postgres:postgres /etc/ssl/certs/server.crt

# Update postgresql.conf
ssl = on
ssl_cert_file = '/etc/ssl/certs/server.crt'
ssl_key_file = '/etc/ssl/private/server.key'
```

### 3. Update Environment for SSL
```bash
# Update .env
DB_SSL=true
DB_SSL_REJECT_UNAUTHORIZED=false  # Set to true with proper certificates
```

## Backup Setup

### 1. Automated Backups
```bash
# Create backup directory
sudo mkdir -p /var/backups/postgresql
sudo chown postgres:postgres /var/backups/postgresql

# Set up cron job
sudo crontab -u postgres -e

# Add these lines:
# Daily backup at 2 AM
0 2 * * * /path/to/backend/scripts/backup-restore.sh daily

# Weekly backup at 3 AM Sunday
0 3 * * 0 /path/to/backend/scripts/backup-restore.sh weekly

# Monthly backup at 4 AM 1st of month
0 4 1 * * /path/to/backend/scripts/backup-restore.sh monthly
```

### 2. Test Backup/Restore
```bash
# Create test backup
./scripts/backup-restore.sh daily

# Verify backup
./scripts/backup-restore.sh verify /var/backups/postgresql/daily/latest.pgdump

# Test restore to different database
./scripts/backup-restore.sh restore /var/backups/postgresql/daily/latest.pgdump test_restore_db
```

This completes the fresh PostgreSQL installation deployment. The database will be optimized and ready for production use.
