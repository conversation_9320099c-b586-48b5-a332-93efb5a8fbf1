# 🐳 Docker Compose Deployment Guide

## Overview

This guide provides comprehensive instructions for deploying the optimized CSRIT backend using Docker Compose with production-ready configurations.

## 📋 Prerequisites

### 1. System Requirements
```bash
# Minimum system requirements
- Docker Engine 20.10+
- Docker Compose 2.0+
- 4GB RAM minimum (8GB recommended)
- 20GB free disk space
- Linux/macOS/Windows with WSL2
```

### 2. Install Docker & Docker Compose
```bash
# Ubuntu/Debian
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Verify installation
docker --version
docker-compose --version
```

## 🚀 Quick Start Deployment

### 1. Environment Configuration
```bash
# Create production environment file
cat > .env.production << 'EOF'
# Database Configuration
DB_NAME=dbcsrit
DB_USER=aistech
DB_PASS=your_secure_password_here
DB_PORT=5432

# Performance Configuration
DB_POOL_MIN=5
DB_POOL_MAX=20
DB_QUERY_TIMEOUT=30000
DB_CONNECTION_TIMEOUT=5000

# Cache Configuration
REDIS_PORT=6379
CACHE_TTL=300

# Application Configuration
APP_PORT=3000
NODE_ENV=production

# Security Configuration
JWT_ACCESS_TOKEN_SECRET=your_jwt_secret_here
JWT_ACCESS_TOKEN_EXPIRATION_TIME=15m
JWT_REFRESH_TOKEN_SECRET=your_refresh_secret_here
JWT_REFRESH_TOKEN_EXPIRATION_TIME=7d
JWT_ACCESS_COOKIE=true

# Monitoring Configuration (Optional)
PGADMIN_EMAIL=<EMAIL>
PGADMIN_PASSWORD=admin123
PGADMIN_PORT=8080
GRAFANA_PASSWORD=admin123
GRAFANA_PORT=3001

# Performance Monitoring
ENABLE_QUERY_LOGGING=false
SLOW_QUERY_THRESHOLD=1000

# Migration Configuration
RUN_SEEDING=true
EOF
```

### 2. Deploy with Optimizations
```bash
# Make scripts executable
chmod +x scripts/*.sh
chmod +x docker/migration/entrypoint.sh

# Deploy the complete stack
docker-compose -f docker-compose.production.yml --env-file .env.production up -d

# Monitor deployment
docker-compose -f docker-compose.production.yml logs -f
```

### 3. Run Database Optimizations
```bash
# Run migration and optimization
docker-compose -f docker-compose.production.yml --profile migration up migration

# Verify optimization
docker-compose -f docker-compose.production.yml exec postgres psql -U aistech -d dbcsrit -c "
SELECT COUNT(*) as optimization_indexes 
FROM pg_indexes 
WHERE schemaname = 'public' AND indexname LIKE 'idx_%';"
```

## 📊 Deployment Profiles

### Basic Deployment (Database + Backend)
```bash
# Deploy core services only
docker-compose -f docker-compose.production.yml up -d postgres redis backend
```

### Full Deployment with Monitoring
```bash
# Deploy with monitoring tools
docker-compose -f docker-compose.production.yml --profile monitoring up -d
```

### Migration Only
```bash
# Run migrations on existing deployment
docker-compose -f docker-compose.production.yml --profile migration up migration
```

## 🔧 Service Configuration

### PostgreSQL Container
- **Image**: postgres:15-alpine
- **Memory**: 512MB-1GB recommended
- **Storage**: Persistent volume for data
- **Extensions**: uuid-ossp, unaccent, pg_trgm, pg_stat_statements
- **Configuration**: Optimized for performance

### Backend Application
- **Image**: Built from Dockerfile.production
- **Memory**: 256MB-512MB recommended
- **Health Check**: HTTP endpoint monitoring
- **Auto-restart**: Unless stopped

### Redis Cache
- **Image**: redis:7-alpine
- **Memory**: 128MB-256MB recommended
- **Persistence**: RDB snapshots enabled

## 📈 Performance Monitoring

### 1. Access Monitoring Tools
```bash
# PgAdmin (Database Management)
http://localhost:8080
# Email: <EMAIL>
# Password: admin123

# Grafana (Performance Dashboards)
http://localhost:3001
# Username: admin
# Password: admin123
```

### 2. Performance Benchmarking
```bash
# Run performance tests
docker-compose -f docker-compose.production.yml exec backend ./scripts/performance-benchmark.sh full

# View results
docker-compose -f docker-compose.production.yml exec backend ls -la /tmp/performance_benchmarks/results/
```

### 3. Real-time Monitoring
```bash
# Monitor container resources
docker stats

# Monitor application logs
docker-compose -f docker-compose.production.yml logs -f backend

# Monitor database logs
docker-compose -f docker-compose.production.yml logs -f postgres
```

## 🔒 Security Configuration

### 1. Network Security
```bash
# The deployment creates an isolated network
# Services communicate internally via service names
# Only necessary ports are exposed to host
```

### 2. Database Security
```bash
# Access database securely
docker-compose -f docker-compose.production.yml exec postgres psql -U aistech -d dbcsrit

# Change default passwords
docker-compose -f docker-compose.production.yml exec postgres psql -U postgres -c "
ALTER USER aistech PASSWORD 'new_secure_password';"
```

### 3. SSL/TLS Configuration (Production)
```bash
# Create SSL certificates directory
mkdir -p docker/ssl

# Generate self-signed certificates (for testing)
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout docker/ssl/server.key \
    -out docker/ssl/server.crt \
    -subj "/CN=localhost"

# Update docker-compose.yml to mount SSL certificates
# Add to postgres service volumes:
# - ./docker/ssl:/etc/ssl/certs
```

## 💾 Backup and Recovery

### 1. Automated Backups
```bash
# Create backup script
cat > backup-docker.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/var/backups/docker-postgres"
mkdir -p "$BACKUP_DIR"

# Create backup
docker-compose -f docker-compose.production.yml exec -T postgres pg_dump -U aistech -d dbcsrit > "$BACKUP_DIR/backup_$(date +%Y%m%d_%H%M%S).sql"

# Compress backup
gzip "$BACKUP_DIR/backup_$(date +%Y%m%d_%H%M%S).sql"

# Clean old backups (keep 7 days)
find "$BACKUP_DIR" -name "*.sql.gz" -mtime +7 -delete
EOF

chmod +x backup-docker.sh

# Set up cron job
echo "0 2 * * * /path/to/backup-docker.sh" | crontab -
```

### 2. Restore from Backup
```bash
# Stop application
docker-compose -f docker-compose.production.yml stop backend

# Restore database
gunzip -c /var/backups/docker-postgres/backup_YYYYMMDD_HHMMSS.sql.gz | \
docker-compose -f docker-compose.production.yml exec -T postgres psql -U aistech -d dbcsrit

# Restart application
docker-compose -f docker-compose.production.yml start backend
```

## 🔄 Scaling and Updates

### 1. Horizontal Scaling
```bash
# Scale backend instances
docker-compose -f docker-compose.production.yml up -d --scale backend=3

# Use load balancer (nginx example)
# Add nginx service to docker-compose.yml
```

### 2. Rolling Updates
```bash
# Update application code
git pull origin main

# Rebuild and deploy
docker-compose -f docker-compose.production.yml build backend
docker-compose -f docker-compose.production.yml up -d --no-deps backend
```

### 3. Database Migrations
```bash
# Run new migrations
docker-compose -f docker-compose.production.yml --profile migration up migration

# Or manually
docker-compose -f docker-compose.production.yml exec backend npm run migration:run
```

## 🚨 Troubleshooting

### Common Issues

#### 1. Database Connection Issues
```bash
# Check database status
docker-compose -f docker-compose.production.yml ps postgres

# Check database logs
docker-compose -f docker-compose.production.yml logs postgres

# Test connection
docker-compose -f docker-compose.production.yml exec postgres pg_isready -U aistech -d dbcsrit
```

#### 2. Application Startup Issues
```bash
# Check application logs
docker-compose -f docker-compose.production.yml logs backend

# Check health status
curl -f http://localhost:3000/health

# Restart application
docker-compose -f docker-compose.production.yml restart backend
```

#### 3. Performance Issues
```bash
# Check resource usage
docker stats

# Run performance benchmark
docker-compose -f docker-compose.production.yml exec backend ./scripts/performance-benchmark.sh content

# Check slow queries
docker-compose -f docker-compose.production.yml exec postgres psql -U aistech -d dbcsrit -c "
SELECT query, mean_exec_time, calls 
FROM pg_stat_statements 
WHERE mean_exec_time > 1000 
ORDER BY mean_exec_time DESC LIMIT 10;"
```

#### 4. Storage Issues
```bash
# Check disk usage
docker system df

# Clean up unused resources
docker system prune -a

# Check volume usage
docker volume ls
```

## 📊 Health Checks

### 1. Service Health
```bash
# Check all services
docker-compose -f docker-compose.production.yml ps

# Check specific service health
docker-compose -f docker-compose.production.yml exec backend curl -f http://localhost:3000/health
```

### 2. Database Health
```bash
# Check database performance
docker-compose -f docker-compose.production.yml exec postgres psql -U aistech -d dbcsrit -c "
SELECT 
    'Active Connections' as metric,
    COUNT(*) as value
FROM pg_stat_activity 
WHERE datname = 'dbcsrit' AND state = 'active';"
```

### 3. Application Metrics
```bash
# Check application metrics endpoint
curl http://localhost:3000/metrics

# Check database optimization status
curl http://localhost:3000/api/health/database
```

## 🎯 Production Checklist

### Pre-deployment
- [ ] Environment variables configured
- [ ] SSL certificates generated (if needed)
- [ ] Backup strategy implemented
- [ ] Monitoring configured
- [ ] Resource limits set

### Post-deployment
- [ ] All services running
- [ ] Database optimizations applied
- [ ] Performance benchmarks completed
- [ ] Monitoring dashboards accessible
- [ ] Backup procedures tested

### Ongoing Maintenance
- [ ] Regular backups scheduled
- [ ] Performance monitoring active
- [ ] Log rotation configured
- [ ] Security updates planned
- [ ] Scaling strategy defined

This Docker deployment provides a production-ready, optimized PostgreSQL backend with comprehensive monitoring and management capabilities.
