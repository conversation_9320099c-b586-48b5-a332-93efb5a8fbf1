# 🌩️ Aiven Cloud PostgreSQL Deployment Guide

## Quick Start

Your Aiven PostgreSQL connection has been configured and is ready for optimization deployment.

### Connection Details
- **Host**: pg-27bc16ff-dimas-13.c.aivencloud.com
- **Port**: 11371
- **Database**: defaultdb
- **User**: avnadmin
- **SSL**: Required

## 🚀 Deploy Optimizations

### Step 1: Verify Configuration
```bash
# Check that .env.local is properly configured
cat .env.local

# Test connection
node -e "
const { Client } = require('pg');
const client = new Client({
  host: 'pg-27bc16ff-dimas-13.c.aivencloud.com',
  port: 11371,
  user: 'avnadmin',
  password: 'AVNS_jUxntoS32yeExaHOO-L',
  database: 'defaultdb',
  ssl: { rejectUnauthorized: false }
});
client.connect().then(() => {
  console.log('✅ Connection successful');
  return client.query('SELECT version()');
}).then(result => {
  console.log('Database:', result.rows[0].version);
  client.end();
}).catch(console.error);
"
```

### Step 2: Run Cloud-Optimized Deployment
```bash
# Make script executable
chmod +x scripts/deploy-aiven-cloud.sh

# Deploy optimizations
./scripts/deploy-aiven-cloud.sh
```

### Step 3: Verify Deployment
```bash
# Check optimization status
./scripts/deploy-aiven-cloud.sh verify

# Test performance
./scripts/deploy-aiven-cloud.sh test
```

## 📊 Expected Results

After successful deployment, you should see:

### Index Creation
```
📊 Optimization indexes created: 25+
✅ Critical index verified: idx_berita_tgl_posting_desc
✅ Critical index verified: idx_chat_messages_room_timestamp
✅ Critical index verified: idx_users_role_status
✅ Critical index verified: idx_user_activity_username_created
```

### Performance Improvements
```
🧪 Testing content query performance...
  ✅ Berita listing: 45ms (was ~150ms)
  ✅ Artikel by category: 32ms (was ~120ms)
  ✅ User with role: 28ms (was ~80ms)
```

## 🔧 Cloud-Specific Optimizations

### Connection Pool Settings
- **Max connections**: 15 (optimized for cloud)
- **Min connections**: 3
- **Connection timeout**: 10 seconds
- **Query timeout**: 30 seconds

### SSL Configuration
- **SSL Mode**: Required
- **Certificate validation**: Disabled (Aiven manages certificates)

### Performance Tuning
- **Keep-alive**: Enabled for stable cloud connections
- **Retry logic**: Built-in connection retry
- **Timeout handling**: Cloud-optimized timeouts

## 🚨 Troubleshooting

### Connection Issues
```bash
# Test direct connection
psql "postgres://avnadmin:<EMAIL>:11371/defaultdb?sslmode=require"

# Check SSL requirements
openssl s_client -connect pg-27bc16ff-dimas-13.c.aivencloud.com:11371 -servername pg-27bc16ff-dimas-13.c.aivencloud.com
```

### Migration Issues
```bash
# Check migration status
npm run migration:show

# Rollback if needed
./scripts/deploy-aiven-cloud.sh rollback
```

### Performance Issues
```bash
# Check slow queries
node -e "
const { Client } = require('pg');
const client = new Client({
  connectionString: 'postgres://avnadmin:<EMAIL>:11371/defaultdb?sslmode=require'
});
client.connect().then(() => {
  return client.query(\`
    SELECT query, mean_exec_time, calls 
    FROM pg_stat_statements 
    WHERE mean_exec_time > 1000 
    ORDER BY mean_exec_time DESC LIMIT 5
  \`);
}).then(result => {
  console.log('Slow queries:', result.rows);
  client.end();
}).catch(console.error);
"
```

## 🎯 Next Steps

1. **Start your application**:
   ```bash
   npm run start:dev
   ```

2. **Monitor performance**:
   - Check API response times
   - Monitor database connections
   - Watch for slow queries

3. **Production deployment**:
   - Update production environment variables
   - Run the same optimization script
   - Monitor performance improvements

## 📈 Performance Monitoring

### Key Metrics to Watch
- **API response times**: Should improve by 60-80%
- **Database connections**: Should be stable around 3-8 active
- **Query execution times**: Most queries under 100ms
- **Error rates**: Should remain at 0%

### Monitoring Commands
```bash
# Check active connections
node -e "
const { Client } = require('pg');
const client = new Client({
  connectionString: 'postgres://avnadmin:<EMAIL>:11371/defaultdb?sslmode=require'
});
client.connect().then(() => {
  return client.query('SELECT COUNT(*) as active_connections FROM pg_stat_activity WHERE state = \\'active\\'');
}).then(result => {
  console.log('Active connections:', result.rows[0].active_connections);
  client.end();
}).catch(console.error);
"

# Check index usage
node -e "
const { Client } = require('pg');
const client = new Client({
  connectionString: 'postgres://avnadmin:<EMAIL>:11371/defaultdb?sslmode=require'
});
client.connect().then(() => {
  return client.query(\`
    SELECT indexname, idx_scan, idx_tup_read 
    FROM pg_stat_user_indexes 
    WHERE schemaname = 'public' AND indexname LIKE 'idx_%' 
    ORDER BY idx_scan DESC LIMIT 10
  \`);
}).then(result => {
  console.log('Top used indexes:');
  result.rows.forEach(row => console.log(\`  \${row.indexname}: \${row.idx_scan} scans\`));
  client.end();
}).catch(console.error);
"
```

Your Aiven cloud database is now ready for high-performance operation! 🚀
