# 🚀 Production Database Optimization Deployment Guide

## Overview

This guide provides step-by-step instructions for deploying database optimizations to production with zero downtime and comprehensive safety measures.

## 📋 Pre-Deployment Checklist

### ✅ Prerequisites
- [ ] Database backup completed and verified
- [ ] All migration files reviewed and tested in staging
- [ ] Performance baseline measurements taken
- [ ] Monitoring systems in place
- [ ] Rollback procedures documented and tested
- [ ] Team notified of deployment window

### ✅ Environment Validation
- [ ] Production database connectivity confirmed
- [ ] Sufficient disk space available (minimum 2x database size)
- [ ] PostgreSQL version compatibility verified
- [ ] Required extensions available (pg_stat_statements, unaccent, pg_trgm)
- [ ] Connection pool settings reviewed

## 🎯 Deployment Strategy

### Phase 1: Pre-Migration Safety (30 minutes)

#### 1.1 Create Full Database Backup
```bash
# Run comprehensive backup
./scripts/backup-restore.sh daily

# Verify backup integrity
./scripts/backup-restore.sh verify /var/backups/postgresql/daily/latest.pgdump
```

#### 1.2 Performance Baseline
```bash
# Capture current performance metrics
./scripts/performance-benchmark.sh full

# Save baseline results
cp -r /tmp/performance_benchmarks/results /tmp/baseline_$(date +%Y%m%d_%H%M%S)
```

#### 1.3 Enable Monitoring
```bash
# Start database monitoring
psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME << 'EOF'
-- Enable query logging temporarily
ALTER SYSTEM SET log_statement = 'all';
ALTER SYSTEM SET log_min_duration_statement = 0;
SELECT pg_reload_conf();
EOF
```

### Phase 2: Critical Configuration Fix (15 minutes)

#### 2.1 Fix Synchronize Setting (CRITICAL)
```typescript
// Update backend/src/source.ts
export const dataSourceOptions: DataSourceOptions & SeederOptions = {
  // ... existing config
  synchronize: false, // ✅ CRITICAL FIX
  migrationsRun: true,
  // ... rest of config
};
```

#### 2.2 Update Application Configuration
```bash
# Build application with new configuration
npm run build

# Restart application (rolling restart recommended)
pm2 restart backend --update-env
```

### Phase 3: Index Creation (60-90 minutes)

#### 3.1 Execute Migration Scripts
```bash
# Run migrations in sequence
npm run migration:run

# Monitor progress
tail -f /var/log/postgresql/postgresql.log
```

#### 3.2 Migration Sequence
1. **Migration 001**: Content Performance Indexes (20-30 min)
2. **Migration 002**: Chat System Indexes (15-20 min)
3. **Migration 003**: User Management Indexes (10-15 min)
4. **Migration 004**: Advanced Performance Indexes (10-15 min)
5. **Migration 005**: Full-Text Search Indexes (15-20 min)

#### 3.3 Monitor Index Creation
```sql
-- Check index creation progress
SELECT 
    schemaname,
    tablename,
    indexname,
    pg_size_pretty(pg_relation_size(indexrelid)) as size
FROM pg_stat_user_indexes 
WHERE schemaname = 'public' 
    AND indexname LIKE 'idx_%'
ORDER BY pg_relation_size(indexrelid) DESC;
```

### Phase 4: Validation & Testing (30 minutes)

#### 4.1 Verify Index Creation
```bash
# Run index analysis
./scripts/performance-benchmark.sh indexes

# Check critical indexes
psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "
SELECT COUNT(*) as index_count 
FROM pg_indexes 
WHERE schemaname = 'public' 
    AND indexname LIKE 'idx_%';"
```

#### 4.2 Performance Validation
```bash
# Run post-optimization benchmark
./scripts/performance-benchmark.sh full

# Compare with baseline
./scripts/compare-performance.sh /tmp/baseline_* /tmp/performance_benchmarks/results
```

#### 4.3 Application Health Check
```bash
# Test critical endpoints
curl -f http://localhost:3000/api/berita
curl -f http://localhost:3000/api/artikel
curl -f http://localhost:3000/api/chat/rooms

# Check application logs
pm2 logs backend --lines 100
```

### Phase 5: Monitoring Setup (15 minutes)

#### 5.1 Enable Production Monitoring
```typescript
// Add to app.module.ts
import { DatabaseMonitorService } from './monitoring/database-monitor.service';

@Module({
  providers: [DatabaseMonitorService],
  // ...
})
```

#### 5.2 Configure Alerts
```bash
# Set up performance monitoring
psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME << 'EOF'
-- Reset monitoring statistics
SELECT pg_stat_reset();
SELECT pg_stat_statements_reset();
EOF
```

## 📊 Expected Performance Improvements

### Content Queries (70-80% improvement)
- **Before**: 100-200ms for content listing
- **After**: 20-50ms for content listing
- **Index Impact**: `idx_berita_tgl_posting_desc`, `idx_artikel_kategori`

### Chat System (60-70% improvement)
- **Before**: 150-300ms for room messages
- **After**: 30-80ms for room messages
- **Index Impact**: `idx_chat_messages_room_timestamp`

### User Management (50-60% improvement)
- **Before**: 80-150ms for user queries
- **After**: 20-60ms for user queries
- **Index Impact**: `idx_users_role_status`

## 🚨 Rollback Procedures

### Emergency Rollback (if issues detected)

#### Option 1: Drop New Indexes
```sql
-- Drop all optimization indexes
DROP INDEX IF EXISTS idx_berita_tgl_posting_desc;
DROP INDEX IF EXISTS idx_chat_messages_room_timestamp;
DROP INDEX IF EXISTS idx_users_role_status;
-- ... (continue for all indexes)
```

#### Option 2: Full Database Restore
```bash
# Restore from backup (LAST RESORT)
./scripts/backup-restore.sh restore /var/backups/postgresql/daily/latest.pgdump production_restore

# Switch application to restored database
# Update environment variables and restart
```

### Rollback Decision Matrix
| Issue | Severity | Action |
|-------|----------|--------|
| Single index causing locks | Low | Drop specific index |
| Multiple query timeouts | Medium | Drop recent indexes |
| Application errors | High | Full rollback |
| Data corruption | Critical | Restore from backup |

## 📈 Post-Deployment Monitoring

### Key Metrics to Monitor (First 24 hours)

#### Database Performance
- Query execution times
- Connection pool utilization
- Cache hit ratio (should be >95%)
- Index usage statistics

#### Application Performance
- API response times
- Error rates
- Memory usage
- CPU utilization

#### Monitoring Queries
```sql
-- Monitor slow queries
SELECT query, mean_exec_time, calls 
FROM pg_stat_statements 
WHERE mean_exec_time > 1000 
ORDER BY mean_exec_time DESC;

-- Check connection usage
SELECT state, COUNT(*) 
FROM pg_stat_activity 
WHERE datname = current_database() 
GROUP BY state;

-- Verify index usage
SELECT schemaname, tablename, indexname, idx_scan 
FROM pg_stat_user_indexes 
WHERE schemaname = 'public' 
    AND indexname LIKE 'idx_%' 
ORDER BY idx_scan DESC;
```

## 🔧 Troubleshooting Guide

### Common Issues and Solutions

#### Issue: Index Creation Taking Too Long
**Symptoms**: Migration stuck on index creation
**Solution**: 
```sql
-- Check for blocking queries
SELECT pid, query, state, query_start 
FROM pg_stat_activity 
WHERE state != 'idle' 
ORDER BY query_start;

-- If necessary, cancel long-running queries
SELECT pg_cancel_backend(pid);
```

#### Issue: High Memory Usage
**Symptoms**: Server memory consumption increased
**Solution**:
```sql
-- Reduce work_mem temporarily
SET work_mem = '64MB';

-- Monitor memory usage
SELECT name, setting, unit 
FROM pg_settings 
WHERE name IN ('shared_buffers', 'work_mem', 'maintenance_work_mem');
```

#### Issue: Application Connection Errors
**Symptoms**: Connection pool exhausted
**Solution**:
```typescript
// Increase connection pool size temporarily
extra: {
  max: 30, // Increase from 20
  min: 8,  // Increase from 5
}
```

## 📞 Emergency Contacts

### Escalation Path
1. **Database Administrator**: [Contact Info]
2. **DevOps Team**: [Contact Info]
3. **Application Team Lead**: [Contact Info]
4. **Infrastructure Team**: [Contact Info]

### Emergency Commands
```bash
# Stop all migrations
ps aux | grep migration | awk '{print $2}' | xargs kill

# Emergency database restart
sudo systemctl restart postgresql

# Application emergency restart
pm2 restart all

# Check system resources
df -h
free -h
top
```

## ✅ Post-Deployment Checklist

### Immediate (0-2 hours)
- [ ] All migrations completed successfully
- [ ] Application responding normally
- [ ] No error spikes in logs
- [ ] Performance improvements visible

### Short-term (2-24 hours)
- [ ] Performance benchmarks show expected improvements
- [ ] No memory leaks detected
- [ ] Index usage statistics look healthy
- [ ] User feedback positive

### Long-term (1-7 days)
- [ ] Performance improvements sustained
- [ ] No unexpected issues reported
- [ ] Monitoring alerts configured
- [ ] Documentation updated

## 📚 Additional Resources

- [PostgreSQL Index Documentation](https://www.postgresql.org/docs/current/indexes.html)
- [TypeORM Migration Guide](https://typeorm.io/migrations)
- [Performance Monitoring Best Practices](./MONITORING_GUIDE.md)
- [Backup and Recovery Procedures](./BACKUP_GUIDE.md)
