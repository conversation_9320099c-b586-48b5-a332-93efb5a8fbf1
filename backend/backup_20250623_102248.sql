--
-- PostgreSQL database dump
--

-- Dumped from database version 16.9
-- Dumped by pg_dump version 17.5

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: public; Type: SCHEMA; Schema: -; Owner: avnadmin
--

-- *not* creating schema, since initdb creates it


ALTER SCHEMA public OWNER TO avnadmin;

--
-- Name: SCHEMA public; Type: COMMENT; Schema: -; Owner: avnadmin
--

COMMENT ON SCHEMA public IS '';


--
-- Name: pg_stat_statements; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS pg_stat_statements WITH SCHEMA public;


--
-- Name: EXTENSION pg_stat_statements; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION pg_stat_statements IS 'track planning and execution statistics of all SQL statements executed';


--
-- Name: pg_trgm; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS pg_trgm WITH SCHEMA public;


--
-- Name: EXTENSION pg_trgm; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION pg_trgm IS 'text similarity measurement and index searching based on trigrams';


--
-- Name: unaccent; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS unaccent WITH SCHEMA public;


--
-- Name: EXTENSION unaccent; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION unaccent IS 'text search dictionary that removes accents';


--
-- Name: uuid-ossp; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA public;


--
-- Name: EXTENSION "uuid-ossp"; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION "uuid-ossp" IS 'generate universally unique identifiers (UUIDs)';


--
-- Name: AppealStatus; Type: TYPE; Schema: public; Owner: avnadmin
--

CREATE TYPE public."AppealStatus" AS ENUM (
    'PENDING',
    'UNDER_REVIEW',
    'APPROVED',
    'REJECTED'
);


ALTER TYPE public."AppealStatus" OWNER TO avnadmin;

--
-- Name: ApprovalActionType; Type: TYPE; Schema: public; Owner: avnadmin
--

CREATE TYPE public."ApprovalActionType" AS ENUM (
    'APPROVE',
    'REJECT',
    'DELEGATE',
    'REQUEST_INFO',
    'ESCALATE',
    'COMMENT',
    'SIGN',
    'REQUEST_CHANGES'
);


ALTER TYPE public."ApprovalActionType" OWNER TO avnadmin;

--
-- Name: ApprovalDecision; Type: TYPE; Schema: public; Owner: avnadmin
--

CREATE TYPE public."ApprovalDecision" AS ENUM (
    'APPROVED',
    'REJECTED',
    'DELEGATED',
    'ESCALATED',
    'RETURNED'
);


ALTER TYPE public."ApprovalDecision" OWNER TO avnadmin;

--
-- Name: ApprovalInstanceStatus; Type: TYPE; Schema: public; Owner: avnadmin
--

CREATE TYPE public."ApprovalInstanceStatus" AS ENUM (
    'PENDING',
    'IN_PROGRESS',
    'APPROVED',
    'REJECTED',
    'CANCELLED',
    'EXPIRED',
    'TIMEOUT'
);


ALTER TYPE public."ApprovalInstanceStatus" OWNER TO avnadmin;

--
-- Name: ApprovalPriority; Type: TYPE; Schema: public; Owner: avnadmin
--

CREATE TYPE public."ApprovalPriority" AS ENUM (
    'LOW',
    'NORMAL',
    'HIGH',
    'URGENT'
);


ALTER TYPE public."ApprovalPriority" OWNER TO avnadmin;

--
-- Name: ApprovalStatus; Type: TYPE; Schema: public; Owner: avnadmin
--

CREATE TYPE public."ApprovalStatus" AS ENUM (
    'PENDING',
    'APPROVED',
    'REJECTED',
    'CANCELLED',
    'EVALUATING_ADMIN',
    'PASSED_ADMIN',
    'FAILED_ADMIN',
    'EVALUATING_TECH',
    'PASSED_TECH',
    'FAILED_TECH',
    'EVALUATING_PRICE',
    'PASSED_PRICE',
    'FAILED_PRICE',
    'EVALUATING_COMMERCIAL',
    'PASSED_COMMERCIAL',
    'FAILED_COMMERCIAL',
    'NEGOTIATING',
    'BACKUP_1',
    'BACKUP_2',
    'SUBMITTED',
    'WINNER',
    'LOSER'
);


ALTER TYPE public."ApprovalStatus" OWNER TO avnadmin;

--
-- Name: ApprovalStepStatus; Type: TYPE; Schema: public; Owner: avnadmin
--

CREATE TYPE public."ApprovalStepStatus" AS ENUM (
    'PENDING',
    'IN_PROGRESS',
    'APPROVED',
    'REJECTED',
    'SKIPPED',
    'EXPIRED',
    'TIMEOUT'
);


ALTER TYPE public."ApprovalStepStatus" OWNER TO avnadmin;

--
-- Name: ApprovalStepType; Type: TYPE; Schema: public; Owner: avnadmin
--

CREATE TYPE public."ApprovalStepType" AS ENUM (
    'APPROVAL',
    'REVIEW',
    'NOTIFICATION',
    'CONDITIONAL',
    'PARALLEL',
    'SEQUENTIAL',
    'ESCALATION',
    'SIGNATURE'
);


ALTER TYPE public."ApprovalStepType" OWNER TO avnadmin;

--
-- Name: ApproverType; Type: TYPE; Schema: public; Owner: avnadmin
--

CREATE TYPE public."ApproverType" AS ENUM (
    'SPECIFIC_USER',
    'ROLE_BASED',
    'DEPARTMENT',
    'HIERARCHY',
    'DYNAMIC',
    'COMMITTEE',
    'EXTERNAL',
    'VALUE_THRESHOLD'
);


ALTER TYPE public."ApproverType" OWNER TO avnadmin;

--
-- Name: ArticleStatus; Type: TYPE; Schema: public; Owner: avnadmin
--

CREATE TYPE public."ArticleStatus" AS ENUM (
    'DRAFT',
    'PENDING_APPROVAL',
    'APPROVED',
    'PUBLISHED',
    'ARCHIVED'
);


ALTER TYPE public."ArticleStatus" OWNER TO avnadmin;

--
-- Name: AuditAction; Type: TYPE; Schema: public; Owner: avnadmin
--

CREATE TYPE public."AuditAction" AS ENUM (
    'CREATE',
    'READ',
    'UPDATE',
    'DELETE',
    'LOGIN',
    'LOGOUT',
    'APPROVE',
    'REJECT',
    'SUBMIT',
    'CANCEL',
    'EXPORT',
    'IMPORT',
    'UPLOAD',
    'DOWNLOAD',
    'SEND_EMAIL',
    'GENERATE_DOCUMENT',
    'SIGN_DOCUMENT',
    'ESCALATE',
    'DELEGATE',
    'COMMENT',
    'BLACKLIST',
    'UNBLACKLIST',
    'VERIFY',
    'SUSPEND',
    'ACTIVATE',
    'ARCHIVE',
    'RESTORE',
    'NEGOTIATE',
    'EVALUATE',
    'AWARD',
    'PRICE_CORRECT',
    'STATUS_CHANGE',
    'WORKFLOW_TRANSITION',
    'VENDOR_REGISTER',
    'VENDOR_VERIFY',
    'OFFER_SUBMIT',
    'DISCUSSION_CREATE',
    'NOTIFICATION_SEND',
    'LOGIN_FAILED',
    'DATABASE_PERFORMANCE',
    'SLOW_QUERY_ANALYSIS'
);


ALTER TYPE public."AuditAction" OWNER TO avnadmin;

--
-- Name: AuditCategory; Type: TYPE; Schema: public; Owner: avnadmin
--

CREATE TYPE public."AuditCategory" AS ENUM (
    'AUTHENTICATION',
    'AUTHORIZATION',
    'DATA_CHANGE',
    'PROCUREMENT',
    'VENDOR_MANAGEMENT',
    'APPROVAL_WORKFLOW',
    'DOCUMENT_MANAGEMENT',
    'FINANCIAL',
    'SECURITY',
    'SYSTEM',
    'GENERAL',
    'BAST_APPROVAL'
);


ALTER TYPE public."AuditCategory" OWNER TO avnadmin;

--
-- Name: AuditSeverity; Type: TYPE; Schema: public; Owner: avnadmin
--

CREATE TYPE public."AuditSeverity" AS ENUM (
    'LOW',
    'MEDIUM',
    'HIGH',
    'CRITICAL'
);


ALTER TYPE public."AuditSeverity" OWNER TO avnadmin;

--
-- Name: BastStatus; Type: TYPE; Schema: public; Owner: avnadmin
--

CREATE TYPE public."BastStatus" AS ENUM (
    'DRAFT',
    'PENDING_APPROVAL',
    'APPROVED',
    'REJECTED'
);


ALTER TYPE public."BastStatus" OWNER TO avnadmin;

--
-- Name: BlacklistCategory; Type: TYPE; Schema: public; Owner: avnadmin
--

CREATE TYPE public."BlacklistCategory" AS ENUM (
    'QUALITY_ISSUES',
    'DELIVERY_DELAYS',
    'CONTRACT_BREACH',
    'FRAUD',
    'CORRUPTION',
    'NON_COMPLIANCE',
    'POOR_PERFORMANCE',
    'LEGAL_ISSUES',
    'OTHER'
);


ALTER TYPE public."BlacklistCategory" OWNER TO avnadmin;

--
-- Name: BlacklistSeverity; Type: TYPE; Schema: public; Owner: avnadmin
--

CREATE TYPE public."BlacklistSeverity" AS ENUM (
    'LOW',
    'MEDIUM',
    'HIGH',
    'CRITICAL'
);


ALTER TYPE public."BlacklistSeverity" OWNER TO avnadmin;

--
-- Name: BlacklistStatus; Type: TYPE; Schema: public; Owner: avnadmin
--

CREATE TYPE public."BlacklistStatus" AS ENUM (
    'ACTIVE',
    'SUSPENDED',
    'EXPIRED',
    'APPEALED',
    'REVOKED'
);


ALTER TYPE public."BlacklistStatus" OWNER TO avnadmin;

--
-- Name: CalculationMethod; Type: TYPE; Schema: public; Owner: avnadmin
--

CREATE TYPE public."CalculationMethod" AS ENUM (
    'WEIGHTED_AVERAGE',
    'SIMPLE_AVERAGE',
    'MINIMUM_SCORE',
    'MAXIMUM_SCORE',
    'CUSTOM_FORMULA'
);


ALTER TYPE public."CalculationMethod" OWNER TO avnadmin;

--
-- Name: ContentStatus; Type: TYPE; Schema: public; Owner: avnadmin
--

CREATE TYPE public."ContentStatus" AS ENUM (
    'DRAFT',
    'PUBLISHED',
    'ARCHIVED'
);


ALTER TYPE public."ContentStatus" OWNER TO avnadmin;

--
-- Name: DeliveryStatus; Type: TYPE; Schema: public; Owner: avnadmin
--

CREATE TYPE public."DeliveryStatus" AS ENUM (
    'SCHEDULED',
    'IN_TRANSIT',
    'DELIVERED',
    'INSPECTING',
    'ACCEPTED',
    'REJECTED',
    'PARTIALLY_ACCEPTED'
);


ALTER TYPE public."DeliveryStatus" OWNER TO avnadmin;

--
-- Name: DiscussionStatus; Type: TYPE; Schema: public; Owner: avnadmin
--

CREATE TYPE public."DiscussionStatus" AS ENUM (
    'ACTIVE',
    'CLOSED',
    'ARCHIVED'
);


ALTER TYPE public."DiscussionStatus" OWNER TO avnadmin;

--
-- Name: DiscussionType; Type: TYPE; Schema: public; Owner: avnadmin
--

CREATE TYPE public."DiscussionType" AS ENUM (
    'AANWIJZING',
    'QA_SESSION',
    'CLARIFICATION',
    'NEGOTIATION',
    'TECHNICAL_DISCUSSION',
    'GENERAL'
);


ALTER TYPE public."DiscussionType" OWNER TO avnadmin;

--
-- Name: DocumentTemplateStatus; Type: TYPE; Schema: public; Owner: avnadmin
--

CREATE TYPE public."DocumentTemplateStatus" AS ENUM (
    'DRAFT',
    'PENDING_APPROVAL',
    'APPROVED',
    'REJECTED',
    'ARCHIVED'
);


ALTER TYPE public."DocumentTemplateStatus" OWNER TO avnadmin;

--
-- Name: DocumentTemplateType; Type: TYPE; Schema: public; Owner: avnadmin
--

CREATE TYPE public."DocumentTemplateType" AS ENUM (
    'RFQ',
    'CONTRACT',
    'PURCHASE_ORDER',
    'INVOICE',
    'BAST',
    'AANWIJZING',
    'EVALUATION_REPORT',
    'AWARD_LETTER',
    'CUSTOM',
    'PURCHASE_REQUISITION',
    'DELIVERY_NOTE'
);


ALTER TYPE public."DocumentTemplateType" OWNER TO avnadmin;

--
-- Name: DocumentType; Type: TYPE; Schema: public; Owner: avnadmin
--

CREATE TYPE public."DocumentType" AS ENUM (
    'RFQ',
    'CONTRACT',
    'PURCHASE_ORDER',
    'INVOICE',
    'BAST',
    'AANWIJZING',
    'EVALUATION_REPORT',
    'AWARD_LETTER',
    'CUSTOM',
    'PURCHASE_REQUISITION',
    'DELIVERY_NOTE'
);


ALTER TYPE public."DocumentType" OWNER TO avnadmin;

--
-- Name: EscalationStatus; Type: TYPE; Schema: public; Owner: avnadmin
--

CREATE TYPE public."EscalationStatus" AS ENUM (
    'PENDING',
    'PROCESSED',
    'CANCELLED'
);


ALTER TYPE public."EscalationStatus" OWNER TO avnadmin;

--
-- Name: EvaluationPeriod; Type: TYPE; Schema: public; Owner: avnadmin
--

CREATE TYPE public."EvaluationPeriod" AS ENUM (
    'MONTHLY',
    'QUARTERLY',
    'SEMI_ANNUAL',
    'ANNUAL',
    'PROJECT_BASED'
);


ALTER TYPE public."EvaluationPeriod" OWNER TO avnadmin;

--
-- Name: EvaluationStatus; Type: TYPE; Schema: public; Owner: avnadmin
--

CREATE TYPE public."EvaluationStatus" AS ENUM (
    'DRAFT',
    'PENDING_REVIEW',
    'APPROVED',
    'PUBLISHED'
);


ALTER TYPE public."EvaluationStatus" OWNER TO avnadmin;

--
-- Name: GoodReceiptStatus; Type: TYPE; Schema: public; Owner: avnadmin
--

CREATE TYPE public."GoodReceiptStatus" AS ENUM (
    'DRAFT',
    'COMPLETED',
    'CANCELLED'
);


ALTER TYPE public."GoodReceiptStatus" OWNER TO avnadmin;

--
-- Name: KpiCategory; Type: TYPE; Schema: public; Owner: avnadmin
--

CREATE TYPE public."KpiCategory" AS ENUM (
    'QUALITY',
    'DELIVERY',
    'COST',
    'SERVICE',
    'COMPLIANCE',
    'INNOVATION',
    'OVERALL'
);


ALTER TYPE public."KpiCategory" OWNER TO avnadmin;

--
-- Name: MeetingType; Type: TYPE; Schema: public; Owner: avnadmin
--

CREATE TYPE public."MeetingType" AS ENUM (
    'PHYSICAL',
    'VIRTUAL',
    'HYBRID'
);


ALTER TYPE public."MeetingType" OWNER TO avnadmin;

--
-- Name: MessageType; Type: TYPE; Schema: public; Owner: avnadmin
--

CREATE TYPE public."MessageType" AS ENUM (
    'MESSAGE',
    'QUESTION',
    'ANSWER',
    'CLARIFICATION',
    'ANNOUNCEMENT',
    'SYSTEM'
);


ALTER TYPE public."MessageType" OWNER TO avnadmin;

--
-- Name: MetricSource; Type: TYPE; Schema: public; Owner: avnadmin
--

CREATE TYPE public."MetricSource" AS ENUM (
    'MANUAL_ENTRY',
    'PROCUREMENT_DATA',
    'CONTRACT_DATA',
    'DELIVERY_DATA',
    'INVOICE_DATA',
    'SURVEY_DATA',
    'SYSTEM_CALCULATED'
);


ALTER TYPE public."MetricSource" OWNER TO avnadmin;

--
-- Name: MetricType; Type: TYPE; Schema: public; Owner: avnadmin
--

CREATE TYPE public."MetricType" AS ENUM (
    'PERCENTAGE',
    'RATIO',
    'COUNT',
    'DURATION',
    'CURRENCY',
    'SCORE',
    'BOOLEAN'
);


ALTER TYPE public."MetricType" OWNER TO avnadmin;

--
-- Name: NegotiationStatus; Type: TYPE; Schema: public; Owner: avnadmin
--

CREATE TYPE public."NegotiationStatus" AS ENUM (
    'PENDING',
    'ACTIVE',
    'PAUSED',
    'COMPLETED',
    'CANCELLED'
);


ALTER TYPE public."NegotiationStatus" OWNER TO avnadmin;

--
-- Name: NotificationChannel; Type: TYPE; Schema: public; Owner: avnadmin
--

CREATE TYPE public."NotificationChannel" AS ENUM (
    'IN_APP',
    'EMAIL',
    'SMS',
    'PUSH',
    'WEBHOOK'
);


ALTER TYPE public."NotificationChannel" OWNER TO avnadmin;

--
-- Name: NotificationPriority; Type: TYPE; Schema: public; Owner: avnadmin
--

CREATE TYPE public."NotificationPriority" AS ENUM (
    'LOW',
    'MEDIUM',
    'HIGH',
    'URGENT'
);


ALTER TYPE public."NotificationPriority" OWNER TO avnadmin;

--
-- Name: NotificationType; Type: TYPE; Schema: public; Owner: avnadmin
--

CREATE TYPE public."NotificationType" AS ENUM (
    'INFO',
    'SUCCESS',
    'WARNING',
    'ERROR'
);


ALTER TYPE public."NotificationType" OWNER TO avnadmin;

--
-- Name: ParticipantRole; Type: TYPE; Schema: public; Owner: avnadmin
--

CREATE TYPE public."ParticipantRole" AS ENUM (
    'MODERATOR',
    'FACILITATOR',
    'PARTICIPANT',
    'OBSERVER'
);


ALTER TYPE public."ParticipantRole" OWNER TO avnadmin;

--
-- Name: PerformanceRating; Type: TYPE; Schema: public; Owner: avnadmin
--

CREATE TYPE public."PerformanceRating" AS ENUM (
    'EXCELLENT',
    'GOOD',
    'SATISFACTORY',
    'NEEDS_IMPROVEMENT',
    'POOR'
);


ALTER TYPE public."PerformanceRating" OWNER TO avnadmin;

--
-- Name: PerformanceTrend; Type: TYPE; Schema: public; Owner: avnadmin
--

CREATE TYPE public."PerformanceTrend" AS ENUM (
    'IMPROVING',
    'STABLE',
    'DECLINING'
);


ALTER TYPE public."PerformanceTrend" OWNER TO avnadmin;

--
-- Name: ProcurementContentType; Type: TYPE; Schema: public; Owner: avnadmin
--

CREATE TYPE public."ProcurementContentType" AS ENUM (
    'GUIDELINE',
    'ANNOUNCEMENT',
    'PROCEDURE',
    'FAQ',
    'TERMS'
);


ALTER TYPE public."ProcurementContentType" OWNER TO avnadmin;

--
-- Name: ProcurementStatus; Type: TYPE; Schema: public; Owner: avnadmin
--

CREATE TYPE public."ProcurementStatus" AS ENUM (
    'DRAFT',
    'PUBLISHED',
    'SUBMISSION_OPEN',
    'SUBMISSION_CLOSED',
    'EVALUATION',
    'AWARDED',
    'COMPLETED',
    'CANCELLED',
    'AANWIJZING',
    'SUBMISSION',
    'NEGOTIATION',
    'WINNER_ANNOUNCEMENT'
);


ALTER TYPE public."ProcurementStatus" OWNER TO avnadmin;

--
-- Name: PublicAssetSecurityLevel; Type: TYPE; Schema: public; Owner: avnadmin
--

CREATE TYPE public."PublicAssetSecurityLevel" AS ENUM (
    'PUBLIC',
    'REGISTERED_USERS',
    'VERIFIED_VENDORS',
    'INTERNAL_ONLY',
    'CONFIDENTIAL',
    'RESTRICTED'
);


ALTER TYPE public."PublicAssetSecurityLevel" OWNER TO avnadmin;

--
-- Name: PublicAssetType; Type: TYPE; Schema: public; Owner: avnadmin
--

CREATE TYPE public."PublicAssetType" AS ENUM (
    'PROCUREMENT_DOCUMENT',
    'TEMPLATE',
    'ANNOUNCEMENT',
    'NEWS_ARTICLE',
    'EVALUATION_RESULT',
    'AWARD_NOTICE',
    'TENDER_DOCUMENT',
    'SPECIFICATION',
    'TERMS_CONDITIONS',
    'USER_GUIDE',
    'FORM',
    'REPORT',
    'IMAGE',
    'VIDEO',
    'AUDIO',
    'OTHER'
);


ALTER TYPE public."PublicAssetType" OWNER TO avnadmin;

--
-- Name: PurchaseRequisitionStatus; Type: TYPE; Schema: public; Owner: avnadmin
--

CREATE TYPE public."PurchaseRequisitionStatus" AS ENUM (
    'DRAFT',
    'PENDING_APPROVAL',
    'APPROVED',
    'REJECTED',
    'CONSOLIDATED',
    'CONVERTED',
    'CANCELLED'
);


ALTER TYPE public."PurchaseRequisitionStatus" OWNER TO avnadmin;

--
-- Name: PurchaseRequisitionType; Type: TYPE; Schema: public; Owner: avnadmin
--

CREATE TYPE public."PurchaseRequisitionType" AS ENUM (
    'INTERNAL',
    'EXTERNAL_ROUTINE',
    'EXTERNAL_NON_ROUTINE'
);


ALTER TYPE public."PurchaseRequisitionType" OWNER TO avnadmin;

--
-- Name: QualityResult; Type: TYPE; Schema: public; Owner: avnadmin
--

CREATE TYPE public."QualityResult" AS ENUM (
    'PASSED',
    'FAILED',
    'CONDITIONAL'
);


ALTER TYPE public."QualityResult" OWNER TO avnadmin;

--
-- Name: QueueJobStatus; Type: TYPE; Schema: public; Owner: avnadmin
--

CREATE TYPE public."QueueJobStatus" AS ENUM (
    'PENDING',
    'PROCESSING',
    'COMPLETED',
    'FAILED',
    'RETRYING',
    'CANCELLED',
    'DELAYED',
    'SENT',
    'PERMANENTLY_FAILED'
);


ALTER TYPE public."QueueJobStatus" OWNER TO avnadmin;

--
-- Name: RoundStatus; Type: TYPE; Schema: public; Owner: avnadmin
--

CREATE TYPE public."RoundStatus" AS ENUM (
    'PENDING',
    'RESPONDED',
    'ACCEPTED',
    'REJECTED',
    'EXPIRED'
);


ALTER TYPE public."RoundStatus" OWNER TO avnadmin;

--
-- Name: UserRoleType; Type: TYPE; Schema: public; Owner: avnadmin
--

CREATE TYPE public."UserRoleType" AS ENUM (
    'ADMIN',
    'PROCUREMENT_USER',
    'APPROVER',
    'VENDOR',
    'COMMITTEE'
);


ALTER TYPE public."UserRoleType" OWNER TO avnadmin;

--
-- Name: VendorStatus; Type: TYPE; Schema: public; Owner: avnadmin
--

CREATE TYPE public."VendorStatus" AS ENUM (
    'PENDING_VERIFICATION',
    'VERIFIED',
    'REJECTED',
    'SUSPENDED',
    'BLACKLISTED'
);


ALTER TYPE public."VendorStatus" OWNER TO avnadmin;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: Approval; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."Approval" (
    id text NOT NULL,
    sequence integer NOT NULL,
    status public."ApprovalStatus" DEFAULT 'PENDING'::public."ApprovalStatus" NOT NULL,
    "approverId" text NOT NULL,
    comments text,
    "processedAt" timestamp(3) without time zone,
    "poId" text,
    "invoiceId" text,
    "bastId" text
);


ALTER TABLE public."Approval" OWNER TO avnadmin;

--
-- Name: ApprovalAction; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."ApprovalAction" (
    id text NOT NULL,
    "stepInstanceId" text NOT NULL,
    action public."ApprovalActionType" NOT NULL,
    decision public."ApprovalDecision",
    comments text,
    "delegatedToId" text,
    metadata jsonb,
    "ipAddress" text,
    "userAgent" text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "performedById" text NOT NULL,
    "actionType" text
);


ALTER TABLE public."ApprovalAction" OWNER TO avnadmin;

--
-- Name: ApprovalComment; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."ApprovalComment" (
    id text NOT NULL,
    "instanceId" text NOT NULL,
    content text NOT NULL,
    "isInternal" boolean DEFAULT false NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL,
    "authorId" text NOT NULL
);


ALTER TABLE public."ApprovalComment" OWNER TO avnadmin;

--
-- Name: ApprovalDelegation; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."ApprovalDelegation" (
    id text NOT NULL,
    "fromUserId" text NOT NULL,
    "toUserId" text NOT NULL,
    "entityType" text NOT NULL,
    reason text,
    "startDate" timestamp(3) without time zone NOT NULL,
    "endDate" timestamp(3) without time zone NOT NULL,
    "isActive" boolean DEFAULT true NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public."ApprovalDelegation" OWNER TO avnadmin;

--
-- Name: ApprovalInstance; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."ApprovalInstance" (
    id text NOT NULL,
    "workflowId" text NOT NULL,
    "entityType" text NOT NULL,
    "entityId" text NOT NULL,
    stage text,
    status public."ApprovalInstanceStatus" DEFAULT 'PENDING'::public."ApprovalInstanceStatus" NOT NULL,
    priority public."ApprovalPriority" DEFAULT 'NORMAL'::public."ApprovalPriority" NOT NULL,
    title text,
    description text,
    metadata jsonb,
    "startedAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "completedAt" timestamp(3) without time zone,
    "dueDate" timestamp(3) without time zone,
    "initiatedById" text NOT NULL,
    context jsonb,
    "purchaseRequisitionId" text,
    "startedBy" text,
    "workflowConfigId" text
);


ALTER TABLE public."ApprovalInstance" OWNER TO avnadmin;

--
-- Name: ApprovalStep; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."ApprovalStep" (
    id text NOT NULL,
    "workflowId" text NOT NULL,
    name text NOT NULL,
    description text,
    sequence integer NOT NULL,
    "stepType" public."ApprovalStepType" NOT NULL,
    "isRequired" boolean DEFAULT true NOT NULL,
    config jsonb,
    "approverType" public."ApproverType" NOT NULL,
    "approverConfig" jsonb,
    "requiredCount" integer DEFAULT 1 NOT NULL,
    "allowDelegation" boolean DEFAULT false NOT NULL,
    "timeoutHours" integer,
    "signatureConfig" jsonb,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public."ApprovalStep" OWNER TO avnadmin;

--
-- Name: ApprovalStepInstance; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."ApprovalStepInstance" (
    id text NOT NULL,
    "instanceId" text NOT NULL,
    "stepId" text NOT NULL,
    status public."ApprovalStepStatus" DEFAULT 'PENDING'::public."ApprovalStepStatus" NOT NULL,
    sequence integer NOT NULL,
    "startedAt" timestamp(3) without time zone,
    "completedAt" timestamp(3) without time zone,
    "dueDate" timestamp(3) without time zone,
    decision public."ApprovalDecision",
    comments text,
    metadata jsonb,
    "approvalInstanceId" text,
    "assignedTo" text
);


ALTER TABLE public."ApprovalStepInstance" OWNER TO avnadmin;

--
-- Name: ApprovalWorkflow; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."ApprovalWorkflow" (
    id text NOT NULL,
    name text NOT NULL,
    description text,
    "entityType" text NOT NULL,
    stage text,
    "isActive" boolean DEFAULT true NOT NULL,
    "isDefault" boolean DEFAULT false NOT NULL,
    version integer DEFAULT 1 NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL,
    conditions jsonb,
    "createdById" text NOT NULL
);


ALTER TABLE public."ApprovalWorkflow" OWNER TO avnadmin;

--
-- Name: ApprovalWorkflowConfig; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."ApprovalWorkflowConfig" (
    id text NOT NULL,
    name text NOT NULL,
    description text,
    "entityType" text NOT NULL,
    stage text,
    "isActive" boolean DEFAULT true NOT NULL,
    config jsonb,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public."ApprovalWorkflowConfig" OWNER TO avnadmin;

--
-- Name: BAST; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."BAST" (
    id text NOT NULL,
    "bastNumber" text NOT NULL,
    "poId" text NOT NULL,
    "handoverDate" timestamp(3) without time zone NOT NULL,
    status public."BastStatus" DEFAULT 'DRAFT'::public."BastStatus" NOT NULL,
    summary text,
    "createdById" text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public."BAST" OWNER TO avnadmin;

--
-- Name: BastChecklistItem; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."BastChecklistItem" (
    id text NOT NULL,
    "bastId" text NOT NULL,
    description text NOT NULL,
    "defectNotes" text,
    "targetDate" timestamp(3) without time zone
);


ALTER TABLE public."BastChecklistItem" OWNER TO avnadmin;

--
-- Name: BlacklistEntry; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."BlacklistEntry" (
    id text NOT NULL,
    "vendorId" text NOT NULL,
    reason text NOT NULL,
    description text,
    severity public."BlacklistSeverity" NOT NULL,
    category public."BlacklistCategory" NOT NULL,
    "startDate" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "endDate" timestamp(3) without time zone,
    "isPermanent" boolean DEFAULT false NOT NULL,
    status public."BlacklistStatus" DEFAULT 'ACTIVE'::public."BlacklistStatus" NOT NULL,
    "appealSubmitted" boolean DEFAULT false NOT NULL,
    "appealDate" timestamp(3) without time zone,
    "appealReason" text,
    "appealStatus" public."AppealStatus",
    "appealDecision" text,
    "appealDecidedAt" timestamp(3) without time zone,
    "appealDecidedById" text,
    "evidenceFiles" jsonb,
    "relatedContracts" jsonb,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL,
    "createdById" text NOT NULL
);


ALTER TABLE public."BlacklistEntry" OWNER TO avnadmin;

--
-- Name: Content; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."Content" (
    id text NOT NULL,
    type text NOT NULL,
    title text NOT NULL,
    slug text,
    content text,
    excerpt text,
    "imageUrl" text,
    "fileUrl" text,
    status public."ContentStatus" DEFAULT 'DRAFT'::public."ContentStatus" NOT NULL,
    "publishedAt" timestamp(3) without time zone,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL,
    metadata jsonb DEFAULT '{}'::jsonb NOT NULL,
    "authorId" text NOT NULL
);


ALTER TABLE public."Content" OWNER TO avnadmin;

--
-- Name: ContentApproval; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."ContentApproval" (
    id text NOT NULL,
    "contentType" text NOT NULL,
    "contentId" text NOT NULL,
    status public."ApprovalStatus" DEFAULT 'PENDING'::public."ApprovalStatus" NOT NULL,
    comments text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL,
    "requestedById" text NOT NULL,
    "reviewedById" text
);


ALTER TABLE public."ContentApproval" OWNER TO avnadmin;

--
-- Name: Contract; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."Contract" (
    id text NOT NULL,
    "contractNumber" text NOT NULL,
    "vendorId" text NOT NULL,
    title text NOT NULL,
    "startDate" timestamp(3) without time zone NOT NULL,
    "endDate" timestamp(3) without time zone NOT NULL,
    "totalValue" double precision NOT NULL,
    status text
);


ALTER TABLE public."Contract" OWNER TO avnadmin;

--
-- Name: Delivery; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."Delivery" (
    id text NOT NULL,
    "deliveryNumber" text NOT NULL,
    "poId" text NOT NULL,
    "deliveryDate" timestamp(3) without time zone NOT NULL,
    "receivedDate" timestamp(3) without time zone,
    status public."DeliveryStatus" DEFAULT 'SCHEDULED'::public."DeliveryStatus" NOT NULL,
    "deliveryAddress" text NOT NULL,
    "receivedBy" text,
    "inspectionDate" timestamp(3) without time zone,
    "inspectionResult" public."QualityResult",
    "inspectionNotes" text,
    "bastNumber" text,
    "bastDate" timestamp(3) without time zone,
    "bastSignedBy" text,
    "bastDocument" text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL,
    "createdById" text NOT NULL,
    "inspectedById" text
);


ALTER TABLE public."Delivery" OWNER TO avnadmin;

--
-- Name: DeliveryItem; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."DeliveryItem" (
    id text NOT NULL,
    "deliveryId" text NOT NULL,
    "poItemId" text NOT NULL,
    "deliveredQty" double precision NOT NULL,
    "acceptedQty" double precision DEFAULT 0 NOT NULL,
    "rejectedQty" double precision DEFAULT 0 NOT NULL,
    "qualityNotes" text,
    "rejectionReason" text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public."DeliveryItem" OWNER TO avnadmin;

--
-- Name: Department; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."Department" (
    id text NOT NULL,
    name text NOT NULL
);


ALTER TABLE public."Department" OWNER TO avnadmin;

--
-- Name: DiscussionAttachment; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."DiscussionAttachment" (
    id text NOT NULL,
    "discussionId" text NOT NULL,
    "fileName" text NOT NULL,
    "originalName" text NOT NULL,
    "fileSize" integer NOT NULL,
    "mimeType" text NOT NULL,
    "filePath" text NOT NULL,
    description text,
    "isPublic" boolean DEFAULT true NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "uploadedById" text NOT NULL
);


ALTER TABLE public."DiscussionAttachment" OWNER TO avnadmin;

--
-- Name: DiscussionMessage; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."DiscussionMessage" (
    id text NOT NULL,
    "discussionId" text NOT NULL,
    content text NOT NULL,
    "messageType" public."MessageType" DEFAULT 'MESSAGE'::public."MessageType" NOT NULL,
    "parentId" text,
    "isEdited" boolean DEFAULT false NOT NULL,
    "isDeleted" boolean DEFAULT false NOT NULL,
    "isOfficial" boolean DEFAULT false NOT NULL,
    "isPublic" boolean DEFAULT true NOT NULL,
    "isAnonymous" boolean DEFAULT false NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL,
    "authorId" text NOT NULL
);


ALTER TABLE public."DiscussionMessage" OWNER TO avnadmin;

--
-- Name: DiscussionParticipant; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."DiscussionParticipant" (
    id text NOT NULL,
    "discussionId" text NOT NULL,
    "userId" text NOT NULL,
    role public."ParticipantRole" DEFAULT 'PARTICIPANT'::public."ParticipantRole" NOT NULL,
    "joinedAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "lastSeenAt" timestamp(3) without time zone,
    "canPost" boolean DEFAULT true NOT NULL,
    "canModerate" boolean DEFAULT false NOT NULL,
    "isActive" boolean DEFAULT true NOT NULL
);


ALTER TABLE public."DiscussionParticipant" OWNER TO avnadmin;

--
-- Name: DiscussionThread; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."DiscussionThread" (
    id text NOT NULL,
    "procurementStageId" text NOT NULL,
    type text NOT NULL
);


ALTER TABLE public."DiscussionThread" OWNER TO avnadmin;

--
-- Name: Document; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."Document" (
    id text NOT NULL,
    "fileName" text NOT NULL,
    "fileUrl" text NOT NULL,
    "fileType" text NOT NULL,
    description text,
    "uploadedAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "vendorId" text,
    "vendorOfferId" text,
    "poId" text,
    "invoiceId" text,
    "bastId" text,
    "procurementId" text,
    "prId" text,
    "documentType" text
);


ALTER TABLE public."Document" OWNER TO avnadmin;

--
-- Name: EvaluationTemplate; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."EvaluationTemplate" (
    id text NOT NULL,
    name text NOT NULL,
    method text NOT NULL,
    "passGrade" double precision,
    criteria jsonb NOT NULL
);


ALTER TABLE public."EvaluationTemplate" OWNER TO avnadmin;

--
-- Name: GoodReceipt; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."GoodReceipt" (
    id text NOT NULL,
    "grNumber" text NOT NULL,
    "poId" text NOT NULL,
    status public."GoodReceiptStatus" DEFAULT 'DRAFT'::public."GoodReceiptStatus" NOT NULL,
    "receivedDate" timestamp(3) without time zone NOT NULL,
    "createdById" text NOT NULL
);


ALTER TABLE public."GoodReceipt" OWNER TO avnadmin;

--
-- Name: GoodReceiptItem; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."GoodReceiptItem" (
    id text NOT NULL,
    "grnId" text,
    "grId" text,
    "purchaseOrderItemId" text NOT NULL,
    "receivedQuantity" double precision NOT NULL,
    notes text
);


ALTER TABLE public."GoodReceiptItem" OWNER TO avnadmin;

--
-- Name: GoodsReceiptNote; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."GoodsReceiptNote" (
    id text NOT NULL,
    "grnNumber" text NOT NULL,
    "poId" text NOT NULL,
    "receivedDate" timestamp(3) without time zone NOT NULL,
    notes text,
    status public."ApprovalStatus" DEFAULT 'PENDING'::public."ApprovalStatus" NOT NULL,
    "approvedAt" timestamp(3) without time zone,
    "approvedById" text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL,
    "createdById" text NOT NULL
);


ALTER TABLE public."GoodsReceiptNote" OWNER TO avnadmin;

--
-- Name: Invoice; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."Invoice" (
    id text NOT NULL,
    "invoiceNumber" text NOT NULL,
    "poId" text NOT NULL,
    "invoiceDate" timestamp(3) without time zone NOT NULL,
    amount double precision NOT NULL,
    status public."ApprovalStatus" DEFAULT 'PENDING'::public."ApprovalStatus" NOT NULL,
    "approvedAt" timestamp(3) without time zone,
    "approvedById" text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL,
    "submittedById" text NOT NULL
);


ALTER TABLE public."Invoice" OWNER TO avnadmin;

--
-- Name: ItemMaster; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."ItemMaster" (
    id text NOT NULL,
    "itemCode" text NOT NULL,
    name text NOT NULL,
    description text,
    unit text NOT NULL,
    category text NOT NULL,
    specifications jsonb
);


ALTER TABLE public."ItemMaster" OWNER TO avnadmin;

--
-- Name: MessageAttachment; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."MessageAttachment" (
    id text NOT NULL,
    "messageId" text NOT NULL,
    "fileName" text NOT NULL,
    "originalName" text NOT NULL,
    "fileSize" integer NOT NULL,
    "mimeType" text NOT NULL,
    "filePath" text NOT NULL,
    description text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "uploadedById" text NOT NULL
);


ALTER TABLE public."MessageAttachment" OWNER TO avnadmin;

--
-- Name: MessageReaction; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."MessageReaction" (
    id text NOT NULL,
    "messageId" text NOT NULL,
    "userId" text NOT NULL,
    emoji text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public."MessageReaction" OWNER TO avnadmin;

--
-- Name: NegotiationRound; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."NegotiationRound" (
    id text NOT NULL,
    "sessionId" text NOT NULL,
    "roundNumber" integer NOT NULL,
    "proposedPrice" double precision,
    "proposedTerms" jsonb,
    "counterPrice" double precision,
    "counterTerms" jsonb,
    status public."RoundStatus" DEFAULT 'PENDING'::public."RoundStatus" NOT NULL,
    "vendorNotes" text,
    "buyerNotes" text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL,
    "proposedById" text NOT NULL,
    "respondedById" text
);


ALTER TABLE public."NegotiationRound" OWNER TO avnadmin;

--
-- Name: NegotiationSession; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."NegotiationSession" (
    id text NOT NULL,
    "procurementId" text NOT NULL,
    "vendorId" text NOT NULL,
    title text NOT NULL,
    description text,
    status public."NegotiationStatus" DEFAULT 'PENDING'::public."NegotiationStatus" NOT NULL,
    "startDate" timestamp(3) without time zone,
    "endDate" timestamp(3) without time zone,
    "scheduledAt" timestamp(3) without time zone,
    "finalPrice" double precision,
    "agreedTerms" jsonb,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL,
    "initiatedById" text NOT NULL
);


ALTER TABLE public."NegotiationSession" OWNER TO avnadmin;

--
-- Name: NewsArticle; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."NewsArticle" (
    id text NOT NULL,
    title text NOT NULL,
    slug text NOT NULL,
    excerpt text,
    content text NOT NULL,
    "featuredImage" text,
    status public."ArticleStatus" DEFAULT 'DRAFT'::public."ArticleStatus" NOT NULL,
    "publishedAt" timestamp(3) without time zone,
    "scheduledAt" timestamp(3) without time zone,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL,
    "authorId" text NOT NULL,
    "categoryId" text NOT NULL
);


ALTER TABLE public."NewsArticle" OWNER TO avnadmin;

--
-- Name: NewsCategory; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."NewsCategory" (
    id text NOT NULL,
    name text NOT NULL,
    slug text NOT NULL,
    description text,
    "isActive" boolean DEFAULT true NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public."NewsCategory" OWNER TO avnadmin;

--
-- Name: NotificationEscalation; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."NotificationEscalation" (
    id text NOT NULL,
    "entityType" text NOT NULL,
    "entityId" text NOT NULL,
    "templateId" text NOT NULL,
    "escalateAt" timestamp(3) without time zone NOT NULL,
    recipients jsonb NOT NULL,
    "escalationTemplate" text,
    status public."EscalationStatus" DEFAULT 'PENDING'::public."EscalationStatus" NOT NULL,
    "processedAt" timestamp(3) without time zone,
    metadata jsonb,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public."NotificationEscalation" OWNER TO avnadmin;

--
-- Name: NotificationTemplate; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."NotificationTemplate" (
    id text NOT NULL,
    name text NOT NULL,
    description text,
    category text NOT NULL,
    subject text NOT NULL,
    "bodyTemplate" text NOT NULL,
    "htmlTemplate" text,
    channels public."NotificationChannel"[],
    priority public."NotificationPriority" DEFAULT 'MEDIUM'::public."NotificationPriority" NOT NULL,
    "isActive" boolean DEFAULT true NOT NULL,
    variables jsonb,
    conditions jsonb,
    "escalationRules" jsonb,
    version integer DEFAULT 1 NOT NULL,
    "parentId" text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL,
    "createdById" text NOT NULL
);


ALTER TABLE public."NotificationTemplate" OWNER TO avnadmin;

--
-- Name: Offer; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."Offer" (
    id text NOT NULL,
    "procurementId" text NOT NULL,
    "vendorId" text NOT NULL,
    "totalAmount" double precision NOT NULL,
    status text NOT NULL,
    "submittedAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public."Offer" OWNER TO avnadmin;

--
-- Name: OfferEvaluation; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."OfferEvaluation" (
    id text NOT NULL,
    "offerId" text NOT NULL,
    "evaluatedById" text NOT NULL,
    "technicalScore" double precision NOT NULL,
    "commercialScore" double precision NOT NULL,
    "totalScore" double precision NOT NULL,
    notes text,
    "evaluatedAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "criteriaScores" jsonb
);


ALTER TABLE public."OfferEvaluation" OWNER TO avnadmin;

--
-- Name: PriceCorrectionLog; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."PriceCorrectionLog" (
    id text NOT NULL,
    "offerItemId" text NOT NULL,
    "originalPrice" double precision NOT NULL,
    "correctedPrice" double precision NOT NULL,
    reason text NOT NULL,
    "correctedAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "correctedById" text NOT NULL
);


ALTER TABLE public."PriceCorrectionLog" OWNER TO avnadmin;

--
-- Name: Procurement; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."Procurement" (
    id text NOT NULL,
    "procurementNumber" text NOT NULL,
    title text NOT NULL,
    description text NOT NULL,
    category text NOT NULL,
    "estimatedValue" double precision NOT NULL,
    currency text DEFAULT 'IDR'::text NOT NULL,
    "publishDate" timestamp(3) without time zone,
    "submissionDeadline" timestamp(3) without time zone NOT NULL,
    "evaluationDate" timestamp(3) without time zone,
    "awardDate" timestamp(3) without time zone,
    status public."ProcurementStatus" DEFAULT 'DRAFT'::public."ProcurementStatus" NOT NULL,
    requirements jsonb,
    "evaluationCriteria" jsonb,
    "workTimeUnit" text,
    "hpsIncludesVat" boolean DEFAULT false NOT NULL,
    "vatRate" double precision,
    "evaluationTemplateId" text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL,
    "createdById" text NOT NULL,
    notes text,
    "ownerEstimate" double precision,
    "showOwnerEstimateToVendor" boolean DEFAULT false NOT NULL,
    type text,
    "workflowTemplateId" text
);


ALTER TABLE public."Procurement" OWNER TO avnadmin;

--
-- Name: ProcurementCommitteeMember; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."ProcurementCommitteeMember" (
    id text NOT NULL,
    "procurementId" text NOT NULL,
    "userId" text NOT NULL,
    role text NOT NULL,
    "assignedAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public."ProcurementCommitteeMember" OWNER TO avnadmin;

--
-- Name: ProcurementContent; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."ProcurementContent" (
    id text NOT NULL,
    title text NOT NULL,
    slug text NOT NULL,
    excerpt text,
    content text NOT NULL,
    type public."ProcurementContentType" NOT NULL,
    status public."ContentStatus" DEFAULT 'DRAFT'::public."ContentStatus" NOT NULL,
    "featuredImage" text,
    "publishedAt" timestamp(3) without time zone,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL,
    "authorId" text NOT NULL
);


ALTER TABLE public."ProcurementContent" OWNER TO avnadmin;

--
-- Name: ProcurementDiscussion; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."ProcurementDiscussion" (
    id text NOT NULL,
    "procurementId" text NOT NULL,
    title text NOT NULL,
    description text,
    type public."DiscussionType" NOT NULL,
    status public."DiscussionStatus" DEFAULT 'ACTIVE'::public."DiscussionStatus" NOT NULL,
    "meetingDate" timestamp(3) without time zone,
    "meetingLocation" text,
    "meetingType" public."MeetingType",
    "maxParticipants" integer,
    "isPublic" boolean DEFAULT true NOT NULL,
    "allowAnonymous" boolean DEFAULT false NOT NULL,
    "startDate" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "endDate" timestamp(3) without time zone,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL,
    "createdById" text NOT NULL
);


ALTER TABLE public."ProcurementDiscussion" OWNER TO avnadmin;

--
-- Name: ProcurementItem; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."ProcurementItem" (
    id text NOT NULL,
    "procurementId" text NOT NULL,
    name text NOT NULL,
    description text,
    quantity double precision NOT NULL,
    unit text NOT NULL,
    "estimatedPrice" double precision NOT NULL,
    specifications jsonb,
    "ownerEstimate" double precision
);


ALTER TABLE public."ProcurementItem" OWNER TO avnadmin;

--
-- Name: ProcurementItemInclude; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."ProcurementItemInclude" (
    id text NOT NULL,
    "itemId" text NOT NULL
);


ALTER TABLE public."ProcurementItemInclude" OWNER TO avnadmin;

--
-- Name: ProcurementMilestone; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."ProcurementMilestone" (
    id text NOT NULL,
    "scheduleId" text NOT NULL,
    name text NOT NULL,
    description text,
    type text NOT NULL,
    "plannedDate" timestamp(3) without time zone NOT NULL,
    "actualDate" timestamp(3) without time zone,
    status text DEFAULT 'PENDING'::text NOT NULL,
    dependencies jsonb,
    "notifyBefore" integer,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public."ProcurementMilestone" OWNER TO avnadmin;

--
-- Name: ProcurementPackage; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."ProcurementPackage" (
    id text NOT NULL,
    name text NOT NULL,
    "createdById" text NOT NULL,
    "procurementId" text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    description text,
    "packageNumber" text,
    status text,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public."ProcurementPackage" OWNER TO avnadmin;

--
-- Name: ProcurementSchedule; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."ProcurementSchedule" (
    id text NOT NULL,
    "procurementId" text NOT NULL,
    "templateId" text,
    schedule jsonb NOT NULL,
    "currentStage" text,
    progress double precision DEFAULT 0 NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL,
    "createdBy" text NOT NULL
);


ALTER TABLE public."ProcurementSchedule" OWNER TO avnadmin;

--
-- Name: ProcurementScheduleTemplate; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."ProcurementScheduleTemplate" (
    id text NOT NULL,
    "templateId" text NOT NULL,
    name text NOT NULL,
    description text,
    stages jsonb NOT NULL,
    milestones jsonb NOT NULL,
    buffers jsonb NOT NULL,
    "workingDays" jsonb NOT NULL,
    holidays jsonb,
    "isActive" boolean DEFAULT true NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL,
    "createdBy" text NOT NULL
);


ALTER TABLE public."ProcurementScheduleTemplate" OWNER TO avnadmin;

--
-- Name: ProcurementStage; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."ProcurementStage" (
    id text NOT NULL,
    "procurementId" text NOT NULL,
    name text NOT NULL,
    description text,
    sequence integer NOT NULL,
    "startDate" timestamp(3) without time zone,
    "endDate" timestamp(3) without time zone,
    status text DEFAULT 'PENDING'::text NOT NULL
);


ALTER TABLE public."ProcurementStage" OWNER TO avnadmin;

--
-- Name: ProcurementVendorRequirement; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."ProcurementVendorRequirement" (
    id text NOT NULL,
    "procurementId" text NOT NULL,
    "requirementId" text NOT NULL,
    "customCriteria" jsonb,
    "customWeight" double precision,
    "isRequired" boolean DEFAULT true NOT NULL
);


ALTER TABLE public."ProcurementVendorRequirement" OWNER TO avnadmin;

--
-- Name: ProcurementWorkflowStageTemplate; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."ProcurementWorkflowStageTemplate" (
    id text NOT NULL,
    "templateId" text NOT NULL,
    name text NOT NULL,
    description text,
    sequence integer NOT NULL,
    type text NOT NULL,
    "isRequired" boolean DEFAULT true NOT NULL,
    config jsonb NOT NULL,
    "minDuration" integer,
    "maxDuration" integer,
    dependencies jsonb,
    "requiresApproval" boolean DEFAULT false NOT NULL,
    "approvalConfig" jsonb,
    "requiredDocuments" jsonb
);


ALTER TABLE public."ProcurementWorkflowStageTemplate" OWNER TO avnadmin;

--
-- Name: ProcurementWorkflowTemplate; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."ProcurementWorkflowTemplate" (
    id text NOT NULL,
    name text NOT NULL,
    description text,
    type text NOT NULL,
    category text NOT NULL,
    "isDefault" boolean DEFAULT false NOT NULL,
    "isActive" boolean DEFAULT true NOT NULL,
    config jsonb NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL,
    "createdBy" text NOT NULL
);


ALTER TABLE public."ProcurementWorkflowTemplate" OWNER TO avnadmin;

--
-- Name: PurchaseOrder; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."PurchaseOrder" (
    id text NOT NULL,
    "poNumber" text NOT NULL,
    "procurementId" text NOT NULL,
    "vendorId" text NOT NULL,
    status public."ApprovalStatus" DEFAULT 'PENDING'::public."ApprovalStatus" NOT NULL,
    "poDate" timestamp(3) without time zone NOT NULL,
    "totalValue" double precision NOT NULL,
    "deliveryAddress" text NOT NULL,
    "termsOfPayment" text NOT NULL,
    "approvedAt" timestamp(3) without time zone,
    "approvedById" text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL,
    "createdById" text NOT NULL
);


ALTER TABLE public."PurchaseOrder" OWNER TO avnadmin;

--
-- Name: PurchaseOrderItem; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."PurchaseOrderItem" (
    id text NOT NULL,
    "poId" text NOT NULL,
    "itemId" text NOT NULL,
    quantity double precision NOT NULL,
    price double precision NOT NULL
);


ALTER TABLE public."PurchaseOrderItem" OWNER TO avnadmin;

--
-- Name: PurchaseRequisition; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."PurchaseRequisition" (
    id text NOT NULL,
    "prNumber" text NOT NULL,
    title text NOT NULL,
    type public."PurchaseRequisitionType" NOT NULL,
    status public."PurchaseRequisitionStatus" DEFAULT 'DRAFT'::public."PurchaseRequisitionStatus" NOT NULL,
    "requesterId" text NOT NULL,
    "totalValue" double precision DEFAULT 0 NOT NULL,
    "isConsolidated" boolean DEFAULT false NOT NULL,
    "packageId" text,
    "procurementId" text,
    "sourceContractId" text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL,
    "consolidatedAt" timestamp(3) without time zone
);


ALTER TABLE public."PurchaseRequisition" OWNER TO avnadmin;

--
-- Name: PurchaseRequisitionItem; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."PurchaseRequisitionItem" (
    id text NOT NULL,
    "prId" text NOT NULL,
    "itemMasterId" text,
    name text NOT NULL,
    description text,
    quantity double precision NOT NULL,
    unit text NOT NULL,
    "estimatedPrice" double precision NOT NULL
);


ALTER TABLE public."PurchaseRequisitionItem" OWNER TO avnadmin;

--
-- Name: ReceiptLog; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."ReceiptLog" (
    id text NOT NULL,
    "goodReceiptItemId" text NOT NULL,
    "quantityChange" double precision NOT NULL,
    "logDate" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    notes text,
    "loggedById" text NOT NULL
);


ALTER TABLE public."ReceiptLog" OWNER TO avnadmin;

--
-- Name: ResourcePermission; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."ResourcePermission" (
    id text NOT NULL,
    resource text NOT NULL,
    action text NOT NULL,
    "roleId" text,
    "userId" text,
    conditions jsonb,
    "isActive" boolean DEFAULT true NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public."ResourcePermission" OWNER TO avnadmin;

--
-- Name: Role; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."Role" (
    id text NOT NULL,
    name text NOT NULL,
    description text,
    permissions jsonb NOT NULL,
    "isActive" boolean DEFAULT true NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public."Role" OWNER TO avnadmin;

--
-- Name: SanctionedIndividual; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."SanctionedIndividual" (
    id text NOT NULL,
    name text NOT NULL,
    "identityNumber" text NOT NULL,
    reason text NOT NULL,
    "sourceBlacklistId" text
);


ALTER TABLE public."SanctionedIndividual" OWNER TO avnadmin;

--
-- Name: TaxExemption; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."TaxExemption" (
    id text NOT NULL,
    "taxTypeId" text NOT NULL,
    "entityType" text NOT NULL,
    "entityId" text NOT NULL,
    reason text NOT NULL,
    "validFrom" timestamp(3) without time zone NOT NULL,
    "validUntil" timestamp(3) without time zone,
    "isActive" boolean DEFAULT true NOT NULL,
    "createdBy" text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public."TaxExemption" OWNER TO avnadmin;

--
-- Name: TaxType; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."TaxType" (
    id text NOT NULL,
    name text NOT NULL,
    description text,
    rate double precision NOT NULL,
    "isActive" boolean DEFAULT true NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public."TaxType" OWNER TO avnadmin;

--
-- Name: User; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."User" (
    id text NOT NULL,
    email text NOT NULL,
    name text NOT NULL,
    password text NOT NULL,
    phone text,
    "isActive" boolean DEFAULT true NOT NULL,
    "emailVerified" boolean DEFAULT false NOT NULL,
    "emailVerifiedAt" timestamp(3) without time zone,
    "lastLoginAt" timestamp(3) without time zone,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL,
    roles public."UserRoleType"[],
    "departmentId" text
);


ALTER TABLE public."User" OWNER TO avnadmin;

--
-- Name: UserRole; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."UserRole" (
    id text NOT NULL,
    "userId" text NOT NULL,
    "roleId" text NOT NULL,
    "assignedAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "assignedBy" text,
    "expiresAt" timestamp(3) without time zone
);


ALTER TABLE public."UserRole" OWNER TO avnadmin;

--
-- Name: Vendor; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."Vendor" (
    id text NOT NULL,
    "userId" text NOT NULL,
    "companyName" text NOT NULL,
    "companyType" text NOT NULL,
    "businessLicense" text NOT NULL,
    "taxId" text NOT NULL,
    address text NOT NULL,
    city text NOT NULL,
    province text NOT NULL,
    "postalCode" text NOT NULL,
    country text DEFAULT 'Indonesia'::text NOT NULL,
    "contactPerson" text NOT NULL,
    "contactPhone" text NOT NULL,
    "contactEmail" text NOT NULL,
    website text,
    "businessCategory" text NOT NULL,
    "businessDescription" text,
    "establishedYear" integer,
    "employeeCount" integer,
    "annualRevenue" double precision,
    status public."VendorStatus" DEFAULT 'PENDING_VERIFICATION'::public."VendorStatus" NOT NULL,
    "verifiedAt" timestamp(3) without time zone,
    "verificationNotes" text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL,
    "isLocked" boolean DEFAULT true NOT NULL,
    "unlockExpiryDate" timestamp(3) without time zone,
    "picName" text,
    "verificationStatus" text
);


ALTER TABLE public."Vendor" OWNER TO avnadmin;

--
-- Name: VendorEvaluation; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."VendorEvaluation" (
    id text NOT NULL,
    "procurementId" text NOT NULL,
    "vendorId" text NOT NULL,
    score double precision NOT NULL,
    notes text,
    "evaluatedAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "evaluatedById" text NOT NULL
);


ALTER TABLE public."VendorEvaluation" OWNER TO avnadmin;

--
-- Name: VendorIssue; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."VendorIssue" (
    id text NOT NULL,
    "vendorId" text NOT NULL,
    title text NOT NULL,
    description text NOT NULL,
    category text NOT NULL,
    severity text NOT NULL,
    status text DEFAULT 'OPEN'::text NOT NULL,
    "reportedById" text NOT NULL,
    "assignedToId" text,
    "reportedAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "resolvedAt" timestamp(3) without time zone,
    "closedAt" timestamp(3) without time zone,
    resolution text,
    "resolutionTime" integer,
    "procurementId" text,
    "purchaseOrderId" text,
    metadata jsonb,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public."VendorIssue" OWNER TO avnadmin;

--
-- Name: VendorKpi; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."VendorKpi" (
    id text NOT NULL,
    "vendorId" text NOT NULL,
    "procurementId" text NOT NULL,
    period text NOT NULL,
    "qualityScore" integer NOT NULL,
    "timeScore" integer NOT NULL,
    "costScore" integer NOT NULL,
    "serviceScore" integer NOT NULL,
    "overallScore" double precision NOT NULL,
    remarks text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public."VendorKpi" OWNER TO avnadmin;

--
-- Name: VendorKpiEvaluation; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."VendorKpiEvaluation" (
    id text NOT NULL,
    "vendorId" text NOT NULL,
    "templateId" text NOT NULL,
    "evaluationPeriod" text NOT NULL,
    "startDate" timestamp(3) without time zone NOT NULL,
    "endDate" timestamp(3) without time zone NOT NULL,
    "overallScore" double precision NOT NULL,
    "categoryScores" jsonb NOT NULL,
    "metricScores" jsonb NOT NULL,
    rating public."PerformanceRating" NOT NULL,
    strengths text,
    weaknesses text,
    recommendations text,
    status public."EvaluationStatus" DEFAULT 'DRAFT'::public."EvaluationStatus" NOT NULL,
    "approvedAt" timestamp(3) without time zone,
    "approvedById" text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL,
    "evaluatedById" text NOT NULL
);


ALTER TABLE public."VendorKpiEvaluation" OWNER TO avnadmin;

--
-- Name: VendorKpiMetric; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."VendorKpiMetric" (
    id text NOT NULL,
    "vendorId" text NOT NULL,
    "metricName" text NOT NULL,
    "metricType" public."MetricType" NOT NULL,
    category public."KpiCategory" NOT NULL,
    "targetValue" double precision,
    "actualValue" double precision NOT NULL,
    "achievementRate" double precision,
    score double precision NOT NULL,
    weight double precision NOT NULL,
    "evaluationPeriod" text NOT NULL,
    "recordedDate" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "sourceType" public."MetricSource" NOT NULL,
    "sourceId" text,
    notes text,
    "isAutoCalculated" boolean DEFAULT false NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "recordedById" text NOT NULL
);


ALTER TABLE public."VendorKpiMetric" OWNER TO avnadmin;

--
-- Name: VendorKpiScore; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."VendorKpiScore" (
    id text NOT NULL,
    "evaluationId" text NOT NULL,
    "metricId" text NOT NULL,
    score double precision NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public."VendorKpiScore" OWNER TO avnadmin;

--
-- Name: VendorKpiTemplate; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."VendorKpiTemplate" (
    id text NOT NULL,
    name text NOT NULL,
    description text,
    category public."KpiCategory" NOT NULL,
    "isActive" boolean DEFAULT true NOT NULL,
    metrics jsonb NOT NULL,
    weights jsonb NOT NULL,
    thresholds jsonb NOT NULL,
    "calculationMethod" public."CalculationMethod" DEFAULT 'WEIGHTED_AVERAGE'::public."CalculationMethod" NOT NULL,
    "evaluationPeriod" public."EvaluationPeriod" DEFAULT 'QUARTERLY'::public."EvaluationPeriod" NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL,
    "createdById" text NOT NULL
);


ALTER TABLE public."VendorKpiTemplate" OWNER TO avnadmin;

--
-- Name: VendorOffer; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."VendorOffer" (
    id text NOT NULL,
    "procurementId" text NOT NULL,
    "vendorId" text NOT NULL,
    "totalPrice" double precision NOT NULL,
    currency text DEFAULT 'IDR'::text NOT NULL,
    "validUntil" timestamp(3) without time zone NOT NULL,
    notes text,
    status public."ApprovalStatus" DEFAULT 'PENDING'::public."ApprovalStatus" NOT NULL,
    "submittedAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "adminScore" double precision,
    "evaluatedAt" timestamp(3) without time zone,
    "evaluatedBy" text,
    "evaluationComments" text,
    "negotiatedPrice" double precision,
    "negotiationComments" text,
    "negotiationNotes" text,
    "priceScore" double precision,
    "submissionDate" timestamp(3) without time zone,
    "techScore" double precision,
    "totalAmount" double precision
);


ALTER TABLE public."VendorOffer" OWNER TO avnadmin;

--
-- Name: VendorOfferItem; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."VendorOfferItem" (
    id text NOT NULL,
    "offerId" text NOT NULL,
    "itemId" text NOT NULL,
    price double precision NOT NULL,
    "correctedPrice" double precision,
    "correctionReason" text,
    "correctedAt" timestamp(3) without time zone,
    "correctedById" text
);


ALTER TABLE public."VendorOfferItem" OWNER TO avnadmin;

--
-- Name: VendorPerformanceHistory; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."VendorPerformanceHistory" (
    id text NOT NULL,
    "vendorId" text NOT NULL,
    period text NOT NULL,
    "overallScore" double precision NOT NULL,
    "qualityScore" double precision,
    "deliveryScore" double precision,
    "costScore" double precision,
    "serviceScore" double precision,
    "complianceScore" double precision,
    rank integer,
    "totalVendors" integer,
    "previousScore" double precision,
    "scoreChange" double precision,
    trend public."PerformanceTrend",
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public."VendorPerformanceHistory" OWNER TO avnadmin;

--
-- Name: VendorRequirementTemplate; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."VendorRequirementTemplate" (
    id text NOT NULL,
    "templateId" text,
    name text NOT NULL,
    description text,
    category text NOT NULL,
    type text NOT NULL,
    criteria jsonb NOT NULL,
    weight double precision,
    "validationRules" jsonb,
    "requiredDocuments" jsonb,
    "isActive" boolean DEFAULT true NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL,
    "createdBy" text NOT NULL
);


ALTER TABLE public."VendorRequirementTemplate" OWNER TO avnadmin;

--
-- Name: VendorVerificationStep; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public."VendorVerificationStep" (
    id text NOT NULL,
    "vendorId" text NOT NULL,
    "stepName" text NOT NULL,
    status public."ApprovalStatus" DEFAULT 'PENDING'::public."ApprovalStatus" NOT NULL,
    "verifiedById" text,
    "verifiedAt" timestamp(3) without time zone,
    comments text
);


ALTER TABLE public."VendorVerificationStep" OWNER TO avnadmin;

--
-- Name: _prisma_migrations; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public._prisma_migrations (
    id character varying(36) NOT NULL,
    checksum character varying(64) NOT NULL,
    finished_at timestamp with time zone,
    migration_name character varying(255) NOT NULL,
    logs text,
    rolled_back_at timestamp with time zone,
    started_at timestamp with time zone DEFAULT now() NOT NULL,
    applied_steps_count integer DEFAULT 0 NOT NULL
);


ALTER TABLE public._prisma_migrations OWNER TO avnadmin;

--
-- Name: about_page_content; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public.about_page_content (
    id text NOT NULL,
    "heroTitle" text DEFAULT 'Tentang E-Procurement'::text NOT NULL,
    "heroSubtitle" text DEFAULT 'PT Bank BPD Sulteng'::text NOT NULL,
    "heroDescription" text DEFAULT 'Sistem pengadaan elektronik yang mendukung transparansi, efisiensi, dan akuntabilitas dalam proses pengadaan barang dan jasa'::text NOT NULL,
    "aboutTitle" text DEFAULT 'Apa itu E-Procurement?'::text NOT NULL,
    "aboutDescription" text DEFAULT 'E-Procurement adalah sistem pengadaan elektronik yang memungkinkan proses pengadaan barang dan jasa dilakukan secara digital, transparan, dan efisien.'::text NOT NULL,
    "companyTitle" text DEFAULT 'Tentang PT Bank BPD Sulteng'::text NOT NULL,
    "companyDescription" text DEFAULT 'Bank Pembangunan Daerah Sulawesi Tengah yang berkomitmen pada pelayanan terbaik'::text NOT NULL,
    "companyName" text DEFAULT 'PT Bank BPD Sulteng'::text NOT NULL,
    "companyFullName" text DEFAULT 'Bank Pembangunan Daerah Sulawesi Tengah'::text NOT NULL,
    "operationalArea" text DEFAULT 'Sulawesi Tengah dan sekitarnya'::text NOT NULL,
    vision text DEFAULT 'Menjadi bank pilihan utama yang mendukung pembangunan ekonomi daerah'::text NOT NULL,
    features jsonb DEFAULT '["Proses tender yang transparan dan fair", "Dokumentasi digital yang lengkap", "Evaluasi vendor yang objektif", "Monitoring real-time status pengadaan", "Integrasi dengan sistem keuangan", "Audit trail yang komprehensif"]'::jsonb NOT NULL,
    "contactTitle" text DEFAULT 'Informasi Kontak'::text NOT NULL,
    "contactDescription" text DEFAULT 'Hubungi kami untuk informasi lebih lanjut tentang e-procurement'::text NOT NULL,
    address text DEFAULT 'Jl. Contoh No. 123
Palu, Sulawesi Tengah
94111'::text NOT NULL,
    phones jsonb DEFAULT '["+62 **********", "+62 **********"]'::jsonb NOT NULL,
    emails jsonb DEFAULT '["<EMAIL>", "<EMAIL>"]'::jsonb NOT NULL,
    "ctaTitle" text DEFAULT 'Siap Bermitra dengan Kami?'::text NOT NULL,
    "ctaDescription" text DEFAULT 'Bergabunglah dengan vendor-vendor terpercaya lainnya dalam sistem e-procurement kami'::text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL,
    "updatedById" text NOT NULL
);


ALTER TABLE public.about_page_content OWNER TO avnadmin;

--
-- Name: audit_logs; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public.audit_logs (
    id text NOT NULL,
    "userId" text,
    "userEmail" text,
    "userRole" text,
    action public."AuditAction" NOT NULL,
    resource text NOT NULL,
    "resourceId" text,
    "oldValues" jsonb,
    "newValues" jsonb,
    "changedFields" jsonb,
    "ipAddress" text,
    "userAgent" text,
    "sessionId" text,
    "requestId" text,
    "procurementId" text,
    "workflowStage" text,
    "approvalStep" text,
    severity public."AuditSeverity" DEFAULT 'LOW'::public."AuditSeverity" NOT NULL,
    category public."AuditCategory" DEFAULT 'GENERAL'::public."AuditCategory" NOT NULL,
    metadata jsonb,
    description text,
    "timestamp" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "partitionDate" date DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    details jsonb,
    "entityId" text,
    "entityType" text
);


ALTER TABLE public.audit_logs OWNER TO avnadmin;

--
-- Name: audit_logs_archive; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public.audit_logs_archive (
    id text NOT NULL,
    "originalId" text NOT NULL,
    "userId" text,
    "userEmail" text,
    "userRole" text,
    action public."AuditAction" NOT NULL,
    resource text NOT NULL,
    "resourceId" text,
    "oldValues" jsonb,
    "newValues" jsonb,
    "changedFields" jsonb,
    "ipAddress" text,
    "userAgent" text,
    "sessionId" text,
    "requestId" text,
    "procurementId" text,
    "workflowStage" text,
    "approvalStep" text,
    severity public."AuditSeverity" NOT NULL,
    category public."AuditCategory" NOT NULL,
    metadata jsonb,
    description text,
    "timestamp" timestamp(3) without time zone NOT NULL,
    "archivedAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "archiveReason" text,
    details jsonb,
    "entityId" text,
    "entityType" text,
    "partitionDate" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.audit_logs_archive OWNER TO avnadmin;

--
-- Name: db_performance_logs; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public.db_performance_logs (
    id text NOT NULL,
    operation text NOT NULL,
    "tableName" text NOT NULL,
    "queryHash" text NOT NULL,
    "executionTime" double precision NOT NULL,
    "rowsAffected" integer,
    "timestamp" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "partitionDate" date DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.db_performance_logs OWNER TO avnadmin;

--
-- Name: document_template_versions; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public.document_template_versions (
    id text NOT NULL,
    "templateId" text NOT NULL,
    version integer NOT NULL,
    content jsonb NOT NULL,
    variables jsonb NOT NULL,
    changelog text,
    "createdBy" text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.document_template_versions OWNER TO avnadmin;

--
-- Name: document_templates; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public.document_templates (
    id text NOT NULL,
    name text NOT NULL,
    description text,
    type public."DocumentTemplateType" NOT NULL,
    category text NOT NULL,
    content jsonb NOT NULL,
    variables jsonb NOT NULL,
    version integer DEFAULT 1 NOT NULL,
    status public."DocumentTemplateStatus" DEFAULT 'DRAFT'::public."DocumentTemplateStatus" NOT NULL,
    "isActive" boolean DEFAULT false NOT NULL,
    "createdBy" text NOT NULL,
    "updatedBy" text,
    "approvedBy" text,
    "approvedAt" timestamp(3) without time zone,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.document_templates OWNER TO avnadmin;

--
-- Name: email_queue; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public.email_queue (
    id text NOT NULL,
    "to" text NOT NULL,
    cc text,
    bcc text,
    subject text NOT NULL,
    body text NOT NULL,
    "htmlBody" text,
    attachments jsonb,
    "templateId" text,
    template text,
    "templateData" jsonb,
    priority public."NotificationPriority" DEFAULT 'MEDIUM'::public."NotificationPriority" NOT NULL,
    status public."QueueJobStatus" DEFAULT 'PENDING'::public."QueueJobStatus" NOT NULL,
    attempts integer DEFAULT 0 NOT NULL,
    "maxAttempts" integer DEFAULT 3 NOT NULL,
    "retryCount" integer DEFAULT 0 NOT NULL,
    "maxRetries" integer DEFAULT 3 NOT NULL,
    "scheduledAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "processedAt" timestamp(3) without time zone,
    "sentAt" timestamp(3) without time zone,
    "failedAt" timestamp(3) without time zone,
    error text,
    metadata jsonb,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.email_queue OWNER TO avnadmin;

--
-- Name: generated_documents; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public.generated_documents (
    id text NOT NULL,
    "templateId" text NOT NULL,
    name text NOT NULL,
    data jsonb NOT NULL,
    content jsonb NOT NULL,
    "pdfUrl" text,
    "createdBy" text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "documentType" public."DocumentType",
    "entityId" text,
    "entityType" text,
    "generatedAt" timestamp(3) without time zone,
    metadata jsonb,
    status text,
    "templateName" text,
    version integer DEFAULT 1 NOT NULL
);


ALTER TABLE public.generated_documents OWNER TO avnadmin;

--
-- Name: migrations; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public.migrations (
    id integer NOT NULL,
    "timestamp" bigint NOT NULL,
    name character varying NOT NULL
);


ALTER TABLE public.migrations OWNER TO avnadmin;

--
-- Name: migrations_id_seq; Type: SEQUENCE; Schema: public; Owner: avnadmin
--

CREATE SEQUENCE public.migrations_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.migrations_id_seq OWNER TO avnadmin;

--
-- Name: migrations_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: avnadmin
--

ALTER SEQUENCE public.migrations_id_seq OWNED BY public.migrations.id;


--
-- Name: notifications; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public.notifications (
    id text NOT NULL,
    "userId" text NOT NULL,
    title text NOT NULL,
    message text NOT NULL,
    type public."NotificationType" DEFAULT 'INFO'::public."NotificationType" NOT NULL,
    "notificationType" text,
    read boolean DEFAULT false NOT NULL,
    metadata jsonb,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.notifications OWNER TO avnadmin;

--
-- Name: public_assets; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public.public_assets (
    id text NOT NULL,
    "fileName" text NOT NULL,
    "originalFileName" text NOT NULL,
    "filePath" text NOT NULL,
    "fileSize" integer,
    size integer,
    "mimeType" text NOT NULL,
    "fileType" public."PublicAssetType" NOT NULL,
    category text,
    type public."PublicAssetType",
    "securityLevel" public."PublicAssetSecurityLevel" DEFAULT 'PUBLIC'::public."PublicAssetSecurityLevel" NOT NULL,
    url text,
    title text,
    description text,
    tags text[] DEFAULT ARRAY[]::text[],
    "isActive" boolean DEFAULT true NOT NULL,
    "isPublic" boolean DEFAULT true NOT NULL,
    "allowDownload" boolean DEFAULT true NOT NULL,
    version integer DEFAULT 1 NOT NULL,
    "parentId" text,
    "publishedAt" timestamp(3) without time zone,
    "expiresAt" timestamp(3) without time zone,
    "archivedAt" timestamp(3) without time zone,
    "downloadCount" integer DEFAULT 0 NOT NULL,
    "viewCount" integer DEFAULT 0 NOT NULL,
    "lastAccessedAt" timestamp(3) without time zone,
    "uploadedById" text NOT NULL,
    metadata jsonb,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.public_assets OWNER TO avnadmin;

--
-- Name: push_queue; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public.push_queue (
    id text NOT NULL,
    "userId" text NOT NULL,
    title text NOT NULL,
    body text NOT NULL,
    data jsonb,
    badge integer,
    sound text,
    priority public."NotificationPriority" DEFAULT 'MEDIUM'::public."NotificationPriority" NOT NULL,
    status public."QueueJobStatus" DEFAULT 'PENDING'::public."QueueJobStatus" NOT NULL,
    attempts integer DEFAULT 0 NOT NULL,
    "maxAttempts" integer DEFAULT 3 NOT NULL,
    "scheduledAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "processedAt" timestamp(3) without time zone,
    "failedAt" timestamp(3) without time zone,
    error text,
    metadata jsonb,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.push_queue OWNER TO avnadmin;

--
-- Name: sms_queue; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public.sms_queue (
    id text NOT NULL,
    "to" text NOT NULL,
    message text NOT NULL,
    "templateId" text,
    "templateData" jsonb,
    priority public."NotificationPriority" DEFAULT 'MEDIUM'::public."NotificationPriority" NOT NULL,
    status public."QueueJobStatus" DEFAULT 'PENDING'::public."QueueJobStatus" NOT NULL,
    attempts integer DEFAULT 0 NOT NULL,
    "maxAttempts" integer DEFAULT 3 NOT NULL,
    "scheduledAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "processedAt" timestamp(3) without time zone,
    "failedAt" timestamp(3) without time zone,
    error text,
    metadata jsonb,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.sms_queue OWNER TO avnadmin;

--
-- Name: webhook_queue; Type: TABLE; Schema: public; Owner: avnadmin
--

CREATE TABLE public.webhook_queue (
    id text NOT NULL,
    url text NOT NULL,
    method text DEFAULT 'POST'::text NOT NULL,
    headers jsonb,
    body jsonb,
    payload jsonb,
    "templateId" text,
    priority public."NotificationPriority" DEFAULT 'MEDIUM'::public."NotificationPriority" NOT NULL,
    status public."QueueJobStatus" DEFAULT 'PENDING'::public."QueueJobStatus" NOT NULL,
    attempts integer DEFAULT 0 NOT NULL,
    "maxAttempts" integer DEFAULT 5 NOT NULL,
    "scheduledAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "processedAt" timestamp(3) without time zone,
    "failedAt" timestamp(3) without time zone,
    error text,
    metadata jsonb,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.webhook_queue OWNER TO avnadmin;

--
-- Name: migrations id; Type: DEFAULT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public.migrations ALTER COLUMN id SET DEFAULT nextval('public.migrations_id_seq'::regclass);


--
-- Data for Name: Approval; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."Approval" (id, sequence, status, "approverId", comments, "processedAt", "poId", "invoiceId", "bastId") FROM stdin;
\.


--
-- Data for Name: ApprovalAction; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."ApprovalAction" (id, "stepInstanceId", action, decision, comments, "delegatedToId", metadata, "ipAddress", "userAgent", "createdAt", "performedById", "actionType") FROM stdin;
\.


--
-- Data for Name: ApprovalComment; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."ApprovalComment" (id, "instanceId", content, "isInternal", "createdAt", "updatedAt", "authorId") FROM stdin;
\.


--
-- Data for Name: ApprovalDelegation; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."ApprovalDelegation" (id, "fromUserId", "toUserId", "entityType", reason, "startDate", "endDate", "isActive", "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: ApprovalInstance; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."ApprovalInstance" (id, "workflowId", "entityType", "entityId", stage, status, priority, title, description, metadata, "startedAt", "completedAt", "dueDate", "initiatedById", context, "purchaseRequisitionId", "startedBy", "workflowConfigId") FROM stdin;
cmc0555le001svonrdd1cay6b	cmc0555430018vonrstitvran	PROCUREMENT	sample-procurement-1	\N	APPROVED	NORMAL	Office Equipment Procurement	Procurement of office computers and printers	\N	2025-06-10 06:27:16.752	2025-06-15 06:27:16.753	\N	cmbzxc3jf0005vopkptwzysem	\N	\N	\N	\N
cmc0555nt001uvonr3o7pddrj	cmc05556h001avonrz9gy1w8r	PROCUREMENT	sample-procurement-2	\N	PENDING	NORMAL	Infrastructure Development	Major infrastructure development project	\N	2025-06-14 06:27:16.753	\N	2025-06-21 06:27:16.753	cmbzxc3jf0005vopkptwzysem	\N	\N	\N	\N
cmc0555qb001wvonrm1qhbezl	cmc0555430018vonrstitvran	PROCUREMENT	sample-procurement-3	\N	IN_PROGRESS	NORMAL	Software Licensing	Annual software license renewal	\N	2025-06-16 06:27:16.753	\N	2025-06-23 06:27:16.753	cmbzxc3jf0005vopkptwzysem	\N	\N	\N	\N
cmc0ecy4y001svo5nrpq1eb95	cmc0ecxhr0018vo5nnscobwqk	PROCUREMENT	sample-procurement-1	\N	APPROVED	NORMAL	Office Equipment Procurement	Procurement of office computers and printers	\N	2025-06-10 10:45:16.879	2025-06-15 10:45:16.88	\N	cmbzxc3jf0005vopkptwzysem	\N	\N	\N	\N
cmc0ecy88001uvo5nfek2rf08	cmc0ecxl5001avo5n0b14508d	PROCUREMENT	sample-procurement-2	\N	PENDING	NORMAL	Infrastructure Development	Major infrastructure development project	\N	2025-06-14 10:45:16.88	\N	2025-06-21 10:45:16.88	cmbzxc3jf0005vopkptwzysem	\N	\N	\N	\N
cmc0ecybh001wvo5n0xfruvzi	cmc0ecxhr0018vo5nnscobwqk	PROCUREMENT	sample-procurement-3	\N	IN_PROGRESS	NORMAL	Software Licensing	Annual software license renewal	\N	2025-06-16 10:45:16.88	\N	2025-06-23 10:45:16.88	cmbzxc3jf0005vopkptwzysem	\N	\N	\N	\N
\.


--
-- Data for Name: ApprovalStep; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."ApprovalStep" (id, "workflowId", name, description, sequence, "stepType", "isRequired", config, "approverType", "approverConfig", "requiredCount", "allowDelegation", "timeoutHours", "signatureConfig", "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: ApprovalStepInstance; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."ApprovalStepInstance" (id, "instanceId", "stepId", status, sequence, "startedAt", "completedAt", "dueDate", decision, comments, metadata, "approvalInstanceId", "assignedTo") FROM stdin;
\.


--
-- Data for Name: ApprovalWorkflow; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."ApprovalWorkflow" (id, name, description, "entityType", stage, "isActive", "isDefault", version, "createdAt", "updatedAt", conditions, "createdById") FROM stdin;
cmc0555430018vonrstitvran	Standard Procurement Approval	Standard approval workflow for procurement under 100M	PROCUREMENT	\N	t	f	1	2025-06-17 06:27:16.131	2025-06-17 06:27:16.131	{"maxValue": 100000000}	cmbzxc3jf0005vopkptwzysem
cmc05556h001avonrz9gy1w8r	High Value Procurement Approval	Enhanced approval workflow for procurement over 100M	PROCUREMENT	\N	t	f	1	2025-06-17 06:27:16.217	2025-06-17 06:27:16.217	{"minValue": 100000000}	cmbzxc3jf0005vopkptwzysem
cmc05557l001cvonrmahvofcj	Emergency Procurement Approval	Fast-track approval for emergency procurements	PROCUREMENT	\N	t	f	1	2025-06-17 06:27:16.258	2025-06-17 06:27:16.258	{"emergency": true}	cmbzxc3jf0005vopkptwzysem
cmc0ecxhr0018vo5nnscobwqk	Standard Procurement Approval	Standard approval workflow for procurement under 100M	PROCUREMENT	\N	t	f	1	2025-06-17 10:45:16.047	2025-06-17 10:45:16.047	{"maxValue": 100000000}	cmbzxc3jf0005vopkptwzysem
cmc0ecxl5001avo5n0b14508d	High Value Procurement Approval	Enhanced approval workflow for procurement over 100M	PROCUREMENT	\N	t	f	1	2025-06-17 10:45:16.169	2025-06-17 10:45:16.169	{"minValue": 100000000}	cmbzxc3jf0005vopkptwzysem
cmc0ecxmx001cvo5npy7dzsht	Emergency Procurement Approval	Fast-track approval for emergency procurements	PROCUREMENT	\N	t	f	1	2025-06-17 10:45:16.233	2025-06-17 10:45:16.233	{"emergency": true}	cmbzxc3jf0005vopkptwzysem
\.


--
-- Data for Name: ApprovalWorkflowConfig; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."ApprovalWorkflowConfig" (id, name, description, "entityType", stage, "isActive", config, "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: BAST; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."BAST" (id, "bastNumber", "poId", "handoverDate", status, summary, "createdById", "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: BastChecklistItem; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."BastChecklistItem" (id, "bastId", description, "defectNotes", "targetDate") FROM stdin;
\.


--
-- Data for Name: BlacklistEntry; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."BlacklistEntry" (id, "vendorId", reason, description, severity, category, "startDate", "endDate", "isPermanent", status, "appealSubmitted", "appealDate", "appealReason", "appealStatus", "appealDecision", "appealDecidedAt", "appealDecidedById", "evidenceFiles", "relatedContracts", "createdAt", "updatedAt", "createdById") FROM stdin;
\.


--
-- Data for Name: Content; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."Content" (id, type, title, slug, content, excerpt, "imageUrl", "fileUrl", status, "publishedAt", "createdAt", "updatedAt", metadata, "authorId") FROM stdin;
\.


--
-- Data for Name: ContentApproval; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."ContentApproval" (id, "contentType", "contentId", status, comments, "createdAt", "updatedAt", "requestedById", "reviewedById") FROM stdin;
\.


--
-- Data for Name: Contract; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."Contract" (id, "contractNumber", "vendorId", title, "startDate", "endDate", "totalValue", status) FROM stdin;
\.


--
-- Data for Name: Delivery; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."Delivery" (id, "deliveryNumber", "poId", "deliveryDate", "receivedDate", status, "deliveryAddress", "receivedBy", "inspectionDate", "inspectionResult", "inspectionNotes", "bastNumber", "bastDate", "bastSignedBy", "bastDocument", "createdAt", "updatedAt", "createdById", "inspectedById") FROM stdin;
\.


--
-- Data for Name: DeliveryItem; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."DeliveryItem" (id, "deliveryId", "poItemId", "deliveredQty", "acceptedQty", "rejectedQty", "qualityNotes", "rejectionReason", "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: Department; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."Department" (id, name) FROM stdin;
\.


--
-- Data for Name: DiscussionAttachment; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."DiscussionAttachment" (id, "discussionId", "fileName", "originalName", "fileSize", "mimeType", "filePath", description, "isPublic", "createdAt", "uploadedById") FROM stdin;
\.


--
-- Data for Name: DiscussionMessage; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."DiscussionMessage" (id, "discussionId", content, "messageType", "parentId", "isEdited", "isDeleted", "isOfficial", "isPublic", "isAnonymous", "createdAt", "updatedAt", "authorId") FROM stdin;
\.


--
-- Data for Name: DiscussionParticipant; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."DiscussionParticipant" (id, "discussionId", "userId", role, "joinedAt", "lastSeenAt", "canPost", "canModerate", "isActive") FROM stdin;
\.


--
-- Data for Name: DiscussionThread; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."DiscussionThread" (id, "procurementStageId", type) FROM stdin;
\.


--
-- Data for Name: Document; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."Document" (id, "fileName", "fileUrl", "fileType", description, "uploadedAt", "vendorId", "vendorOfferId", "poId", "invoiceId", "bastId", "procurementId", "prId", "documentType") FROM stdin;
\.


--
-- Data for Name: EvaluationTemplate; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."EvaluationTemplate" (id, name, method, "passGrade", criteria) FROM stdin;
\.


--
-- Data for Name: GoodReceipt; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."GoodReceipt" (id, "grNumber", "poId", status, "receivedDate", "createdById") FROM stdin;
\.


--
-- Data for Name: GoodReceiptItem; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."GoodReceiptItem" (id, "grnId", "grId", "purchaseOrderItemId", "receivedQuantity", notes) FROM stdin;
\.


--
-- Data for Name: GoodsReceiptNote; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."GoodsReceiptNote" (id, "grnNumber", "poId", "receivedDate", notes, status, "approvedAt", "approvedById", "createdAt", "updatedAt", "createdById") FROM stdin;
\.


--
-- Data for Name: Invoice; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."Invoice" (id, "invoiceNumber", "poId", "invoiceDate", amount, status, "approvedAt", "approvedById", "createdAt", "updatedAt", "submittedById") FROM stdin;
\.


--
-- Data for Name: ItemMaster; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."ItemMaster" (id, "itemCode", name, description, unit, category, specifications) FROM stdin;
\.


--
-- Data for Name: MessageAttachment; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."MessageAttachment" (id, "messageId", "fileName", "originalName", "fileSize", "mimeType", "filePath", description, "createdAt", "uploadedById") FROM stdin;
\.


--
-- Data for Name: MessageReaction; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."MessageReaction" (id, "messageId", "userId", emoji, "createdAt") FROM stdin;
\.


--
-- Data for Name: NegotiationRound; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."NegotiationRound" (id, "sessionId", "roundNumber", "proposedPrice", "proposedTerms", "counterPrice", "counterTerms", status, "vendorNotes", "buyerNotes", "createdAt", "updatedAt", "proposedById", "respondedById") FROM stdin;
\.


--
-- Data for Name: NegotiationSession; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."NegotiationSession" (id, "procurementId", "vendorId", title, description, status, "startDate", "endDate", "scheduledAt", "finalPrice", "agreedTerms", "createdAt", "updatedAt", "initiatedById") FROM stdin;
\.


--
-- Data for Name: NewsArticle; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."NewsArticle" (id, title, slug, excerpt, content, "featuredImage", status, "publishedAt", "scheduledAt", "createdAt", "updatedAt", "authorId", "categoryId") FROM stdin;
\.


--
-- Data for Name: NewsCategory; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."NewsCategory" (id, name, slug, description, "isActive", "createdAt", "updatedAt") FROM stdin;
cmbzxfd0t0014voydqxrsrxsl	Pengumuman	pengumuman	Pengumuman resmi sistem e-procurement	t	2025-06-17 02:51:16.014	2025-06-17 02:51:16.014
cmbzxfda50015voydgq1s1e2v	Berita	berita	Berita terkait pengadaan barang dan jasa	t	2025-06-17 02:51:16.349	2025-06-17 02:51:16.349
cmbzxfdfx0016voydwdqzhnr1	Tender	tender	Informasi tender dan lelang pengadaan	t	2025-06-17 02:51:16.557	2025-06-17 02:51:16.557
\.


--
-- Data for Name: NotificationEscalation; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."NotificationEscalation" (id, "entityType", "entityId", "templateId", "escalateAt", recipients, "escalationTemplate", status, "processedAt", metadata, "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: NotificationTemplate; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."NotificationTemplate" (id, name, description, category, subject, "bodyTemplate", "htmlTemplate", channels, priority, "isActive", variables, conditions, "escalationRules", version, "parentId", "createdAt", "updatedAt", "createdById") FROM stdin;
\.


--
-- Data for Name: Offer; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."Offer" (id, "procurementId", "vendorId", "totalAmount", status, "submittedAt") FROM stdin;
\.


--
-- Data for Name: OfferEvaluation; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."OfferEvaluation" (id, "offerId", "evaluatedById", "technicalScore", "commercialScore", "totalScore", notes, "evaluatedAt", "criteriaScores") FROM stdin;
\.


--
-- Data for Name: PriceCorrectionLog; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."PriceCorrectionLog" (id, "offerItemId", "originalPrice", "correctedPrice", reason, "correctedAt", "correctedById") FROM stdin;
\.


--
-- Data for Name: Procurement; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."Procurement" (id, "procurementNumber", title, description, category, "estimatedValue", currency, "publishDate", "submissionDeadline", "evaluationDate", "awardDate", status, requirements, "evaluationCriteria", "workTimeUnit", "hpsIncludesVat", "vatRate", "evaluationTemplateId", "createdAt", "updatedAt", "createdById", notes, "ownerEstimate", "showOwnerEstimateToVendor", type, "workflowTemplateId") FROM stdin;
\.


--
-- Data for Name: ProcurementCommitteeMember; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."ProcurementCommitteeMember" (id, "procurementId", "userId", role, "assignedAt") FROM stdin;
\.


--
-- Data for Name: ProcurementContent; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."ProcurementContent" (id, title, slug, excerpt, content, type, status, "featuredImage", "publishedAt", "createdAt", "updatedAt", "authorId") FROM stdin;
\.


--
-- Data for Name: ProcurementDiscussion; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."ProcurementDiscussion" (id, "procurementId", title, description, type, status, "meetingDate", "meetingLocation", "meetingType", "maxParticipants", "isPublic", "allowAnonymous", "startDate", "endDate", "createdAt", "updatedAt", "createdById") FROM stdin;
\.


--
-- Data for Name: ProcurementItem; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."ProcurementItem" (id, "procurementId", name, description, quantity, unit, "estimatedPrice", specifications, "ownerEstimate") FROM stdin;
\.


--
-- Data for Name: ProcurementItemInclude; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."ProcurementItemInclude" (id, "itemId") FROM stdin;
\.


--
-- Data for Name: ProcurementMilestone; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."ProcurementMilestone" (id, "scheduleId", name, description, type, "plannedDate", "actualDate", status, dependencies, "notifyBefore", "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: ProcurementPackage; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."ProcurementPackage" (id, name, "createdById", "procurementId", "createdAt", description, "packageNumber", status, "updatedAt") FROM stdin;
\.


--
-- Data for Name: ProcurementSchedule; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."ProcurementSchedule" (id, "procurementId", "templateId", schedule, "currentStage", progress, "createdAt", "updatedAt", "createdBy") FROM stdin;
\.


--
-- Data for Name: ProcurementScheduleTemplate; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."ProcurementScheduleTemplate" (id, "templateId", name, description, stages, milestones, buffers, "workingDays", holidays, "isActive", "createdAt", "updatedAt", "createdBy") FROM stdin;
cmc0555hj001ovonrm8xl8cyo	cmc05558s001evonrn4ci8jnd	IT Procurement Schedule	Standard timeline for IT procurement	{"tender": {"duration": 14, "dependencies": ["planning"]}, "planning": {"duration": 7, "dependencies": []}, "evaluation": {"duration": 7, "dependencies": ["tender"]}}	{"tender_opening": {"stage": "tender", "offset": 0}, "evaluation_complete": {"stage": "evaluation", "offset": 7}}	{"tender": 2, "planning": 1, "evaluation": 1}	{"friday": true, "monday": true, "tuesday": true, "thursday": true, "wednesday": true}	\N	t	2025-06-17 06:27:16.615	2025-06-17 06:27:16.615	cmbzxc3jf0005vopkptwzysem
cmc0555k3001qvonrcwk37xxy	cmc0555bc001gvonrr7l8d6od	Construction Schedule	Timeline for construction projects	{"design": {"duration": 14, "dependencies": ["planning"]}, "tender": {"duration": 30, "dependencies": ["design"]}, "planning": {"duration": 21, "dependencies": []}}	{"design_approval": {"stage": "design", "offset": 14}, "tender_submission": {"stage": "tender", "offset": 30}}	{"design": 2, "tender": 5, "planning": 3}	{"friday": true, "monday": true, "tuesday": true, "thursday": true, "wednesday": true}	\N	t	2025-06-17 06:27:16.707	2025-06-17 06:27:16.707	cmbzxc3jf0005vopkptwzysem
cmc0ecy05001ovo5nsu3uyg4v	cmc0ecxof001evo5nlfnzj7vh	IT Procurement Schedule	Standard timeline for IT procurement	{"tender": {"duration": 14, "dependencies": ["planning"]}, "planning": {"duration": 7, "dependencies": []}, "evaluation": {"duration": 7, "dependencies": ["tender"]}}	{"tender_opening": {"stage": "tender", "offset": 0}, "evaluation_complete": {"stage": "evaluation", "offset": 7}}	{"tender": 2, "planning": 1, "evaluation": 1}	{"friday": true, "monday": true, "tuesday": true, "thursday": true, "wednesday": true}	\N	t	2025-06-17 10:45:16.709	2025-06-17 10:45:16.709	cmbzxc3jf0005vopkptwzysem
cmc0ecy3d001qvo5nk5id88x8	cmc0ecxrn001gvo5n6zql32jk	Construction Schedule	Timeline for construction projects	{"design": {"duration": 14, "dependencies": ["planning"]}, "tender": {"duration": 30, "dependencies": ["design"]}, "planning": {"duration": 21, "dependencies": []}}	{"design_approval": {"stage": "design", "offset": 14}, "tender_submission": {"stage": "tender", "offset": 30}}	{"design": 2, "tender": 5, "planning": 3}	{"friday": true, "monday": true, "tuesday": true, "thursday": true, "wednesday": true}	\N	t	2025-06-17 10:45:16.825	2025-06-17 10:45:16.825	cmbzxc3jf0005vopkptwzysem
\.


--
-- Data for Name: ProcurementStage; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."ProcurementStage" (id, "procurementId", name, description, sequence, "startDate", "endDate", status) FROM stdin;
\.


--
-- Data for Name: ProcurementVendorRequirement; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."ProcurementVendorRequirement" (id, "procurementId", "requirementId", "customCriteria", "customWeight", "isRequired") FROM stdin;
\.


--
-- Data for Name: ProcurementWorkflowStageTemplate; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."ProcurementWorkflowStageTemplate" (id, "templateId", name, description, sequence, type, "isRequired", config, "minDuration", "maxDuration", dependencies, "requiresApproval", "approvalConfig", "requiredDocuments") FROM stdin;
\.


--
-- Data for Name: ProcurementWorkflowTemplate; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."ProcurementWorkflowTemplate" (id, name, description, type, category, "isDefault", "isActive", config, "createdAt", "updatedAt", "createdBy") FROM stdin;
cmc05558s001evonrn4ci8jnd	IT Equipment Procurement	Template for IT equipment and software procurement	TENDER	GOODS	f	t	{"stages": ["planning", "specification", "tender", "evaluation", "award"], "department": "IT", "estimatedDuration": 30}	2025-06-17 06:27:16.301	2025-06-17 06:27:16.301	cmbzxc3jf0005vopkptwzysem
cmc0555bc001gvonrr7l8d6od	Construction Services	Template for construction and infrastructure services	TENDER	CONSTRUCTION	f	t	{"stages": ["planning", "design", "tender", "evaluation", "award", "execution"], "department": "Infrastructure", "estimatedDuration": 60}	2025-06-17 06:27:16.392	2025-06-17 06:27:16.392	cmbzxc3jf0005vopkptwzysem
cmc0555cj001ivonr0ggnuvii	Consulting Services	Template for professional consulting services	RFQ	SERVICES	f	t	{"stages": ["planning", "rfq", "proposal", "evaluation", "award"], "department": "General", "estimatedDuration": 21}	2025-06-17 06:27:16.435	2025-06-17 06:27:16.435	cmbzxc3jf0005vopkptwzysem
cmc0ecxof001evo5nlfnzj7vh	IT Equipment Procurement	Template for IT equipment and software procurement	TENDER	GOODS	f	t	{"stages": ["planning", "specification", "tender", "evaluation", "award"], "department": "IT", "estimatedDuration": 30}	2025-06-17 10:45:16.288	2025-06-17 10:45:16.288	cmbzxc3jf0005vopkptwzysem
cmc0ecxrn001gvo5n6zql32jk	Construction Services	Template for construction and infrastructure services	TENDER	CONSTRUCTION	f	t	{"stages": ["planning", "design", "tender", "evaluation", "award", "execution"], "department": "Infrastructure", "estimatedDuration": 60}	2025-06-17 10:45:16.403	2025-06-17 10:45:16.403	cmbzxc3jf0005vopkptwzysem
cmc0ecxt5001ivo5ndhy0udf2	Consulting Services	Template for professional consulting services	RFQ	SERVICES	f	t	{"stages": ["planning", "rfq", "proposal", "evaluation", "award"], "department": "General", "estimatedDuration": 21}	2025-06-17 10:45:16.457	2025-06-17 10:45:16.457	cmbzxc3jf0005vopkptwzysem
\.


--
-- Data for Name: PurchaseOrder; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."PurchaseOrder" (id, "poNumber", "procurementId", "vendorId", status, "poDate", "totalValue", "deliveryAddress", "termsOfPayment", "approvedAt", "approvedById", "createdAt", "updatedAt", "createdById") FROM stdin;
\.


--
-- Data for Name: PurchaseOrderItem; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."PurchaseOrderItem" (id, "poId", "itemId", quantity, price) FROM stdin;
\.


--
-- Data for Name: PurchaseRequisition; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."PurchaseRequisition" (id, "prNumber", title, type, status, "requesterId", "totalValue", "isConsolidated", "packageId", "procurementId", "sourceContractId", "createdAt", "updatedAt", "consolidatedAt") FROM stdin;
\.


--
-- Data for Name: PurchaseRequisitionItem; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."PurchaseRequisitionItem" (id, "prId", "itemMasterId", name, description, quantity, unit, "estimatedPrice") FROM stdin;
\.


--
-- Data for Name: ReceiptLog; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."ReceiptLog" (id, "goodReceiptItemId", "quantityChange", "logDate", notes, "loggedById") FROM stdin;
\.


--
-- Data for Name: ResourcePermission; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."ResourcePermission" (id, resource, action, "roleId", "userId", conditions, "isActive", "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: Role; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."Role" (id, name, description, permissions, "isActive", "createdAt", "updatedAt") FROM stdin;
cmbzxc2j20000vopkwj4t1gh7	ADMIN	System Administrator	["user.create", "user.read", "user.update", "user.delete", "vendor.create", "vendor.read", "vendor.update", "vendor.delete", "vendor.verify", "procurement.create", "procurement.read", "procurement.update", "procurement.delete", "approval.create", "approval.read", "approval.update", "approval.delete", "document.create", "document.read", "document.update", "document.delete", "system.configure", "audit.read", "report.generate"]	t	2025-06-17 02:48:42.446	2025-06-17 02:48:42.446
cmbzxc2sk0001vopkj3h6y229	PROCUREMENT_USER	Procurement Officer	["procurement.create", "procurement.read", "procurement.update", "vendor.read", "vendor.verify", "approval.read", "approval.approve", "document.create", "document.read"]	t	2025-06-17 02:48:42.788	2025-06-17 02:48:42.788
cmbzxc2yd0002vopkesh6wc7l	APPROVER	Approval Authority	["procurement.read", "vendor.read", "approval.read", "approval.approve", "approval.reject", "document.read"]	t	2025-06-17 02:48:42.997	2025-06-17 02:48:42.997
cmbzxc3470003vopkbonw8fy1	VENDOR	Vendor/Supplier	["procurement.read", "offer.create", "offer.read", "offer.update", "document.create", "document.read", "discussion.read", "discussion.participate"]	t	2025-06-17 02:48:43.208	2025-06-17 02:48:43.208
cmbzxc3a40004vopkm9w074fw	COMMITTEE	Procurement Committee Member	["procurement.read", "procurement.evaluate", "vendor.read", "vendor.evaluate", "approval.read", "approval.approve", "document.read", "document.create"]	t	2025-06-17 02:48:43.421	2025-06-17 02:48:43.421
\.


--
-- Data for Name: SanctionedIndividual; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."SanctionedIndividual" (id, name, "identityNumber", reason, "sourceBlacklistId") FROM stdin;
\.


--
-- Data for Name: TaxExemption; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."TaxExemption" (id, "taxTypeId", "entityType", "entityId", reason, "validFrom", "validUntil", "isActive", "createdBy", "createdAt") FROM stdin;
\.


--
-- Data for Name: TaxType; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."TaxType" (id, name, description, rate, "isActive", "createdAt", "updatedAt") FROM stdin;
cmbzxfc3q000zvoydfm6y5grz	PPN	Pajak Pertambahan Nilai	11	t	2025-06-17 02:51:14.823	2025-06-17 02:51:14.823
cmbzxfcd20010voydffjtt9kl	PPh 21	Pajak Penghasilan Pasal 21	5	t	2025-06-17 02:51:15.158	2025-06-17 02:51:15.158
cmbzxfciy0011voyd3rprqmtg	PPh 22	Pajak Penghasilan Pasal 22	1.5	t	2025-06-17 02:51:15.37	2025-06-17 02:51:15.37
cmbzxfcp40012voydcj4v9vym	PPh 23	Pajak Penghasilan Pasal 23	2	t	2025-06-17 02:51:15.592	2025-06-17 02:51:15.592
cmbzxfcv10013voydv48arxsb	PPh 4(2)	Pajak Penghasilan Pasal 4 ayat 2	10	t	2025-06-17 02:51:15.805	2025-06-17 02:51:15.805
\.


--
-- Data for Name: User; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."User" (id, email, name, password, phone, "isActive", "emailVerified", "emailVerifiedAt", "lastLoginAt", "createdAt", "updatedAt", roles, "departmentId") FROM stdin;
cmbzxc3jf0005vopkptwzysem	<EMAIL>	System Administrator	$2b$10$g3kxcUbsa0RgevzOgDOQnO1xY3aKLStcAb9pCP51qUReMer4rjmle	+62-21-1234567	t	t	2025-06-17 02:48:43.754	\N	2025-06-17 02:48:43.756	2025-06-17 02:48:43.756	{ADMIN}	\N
cmbzxf93l0008voydmmjpd0nx	<EMAIL>	Budi Santoso	$2b$10$TQMx.QpHhLhydPEykvw6AuspGNY.lgxVlTwv6ywYDxTPidhLJifg2	+62-21-1234568	t	t	2025-06-17 02:51:10.927	\N	2025-06-17 02:51:10.929	2025-06-17 02:51:10.929	{PROCUREMENT_USER}	\N
cmbzxf9ha000bvoyduleboh82	<EMAIL>	Siti Rahayu	$2b$10$TQMx.QpHhLhydPEykvw6AuspGNY.lgxVlTwv6ywYDxTPidhLJifg2	+62-21-1234569	t	t	2025-06-17 02:51:11.422	\N	2025-06-17 02:51:11.423	2025-06-17 02:51:11.423	{PROCUREMENT_USER}	\N
cmbzxf9t0000evoydmhh563od	<EMAIL>	Dr. Ahmad Wijaya	$2b$10$TQMx.QpHhLhydPEykvw6AuspGNY.lgxVlTwv6ywYDxTPidhLJifg2	+62-21-1234570	t	t	2025-06-17 02:51:11.842	\N	2025-06-17 02:51:11.844	2025-06-17 02:51:11.844	{APPROVER}	\N
cmbzxfa4s000hvoydl623q6wo	<EMAIL>	Ir. Maria Susanti	$2b$10$TQMx.QpHhLhydPEykvw6AuspGNY.lgxVlTwv6ywYDxTPidhLJifg2	+62-21-1234571	t	t	2025-06-17 02:51:12.267	\N	2025-06-17 02:51:12.268	2025-06-17 02:51:12.268	{APPROVER}	\N
cmbzxfagj000kvoydwacf7ft2	<EMAIL>	PT. Technology Corporation	$2b$10$TQMx.QpHhLhydPEykvw6AuspGNY.lgxVlTwv6ywYDxTPidhLJifg2	\N	t	t	2025-06-17 02:51:12.689	\N	2025-06-17 02:51:12.69	2025-06-17 02:51:12.69	{VENDOR}	\N
cmbzxfb2x000pvoydpfuwqi4z	<EMAIL>	CV. Build Pro Construction	$2b$10$TQMx.QpHhLhydPEykvw6AuspGNY.lgxVlTwv6ywYDxTPidhLJifg2	\N	t	t	2025-06-17 02:51:13.495	\N	2025-06-17 02:51:13.497	2025-06-17 02:51:13.497	{VENDOR}	\N
cmbzxfbkm000uvoydavguvo47	<EMAIL>	PT. Supply Corporation	$2b$10$TQMx.QpHhLhydPEykvw6AuspGNY.lgxVlTwv6ywYDxTPidhLJifg2	\N	t	t	2025-06-17 02:51:14.132	\N	2025-06-17 02:51:14.133	2025-06-17 02:51:14.133	{VENDOR}	\N
\.


--
-- Data for Name: UserRole; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."UserRole" (id, "userId", "roleId", "assignedAt", "assignedBy", "expiresAt") FROM stdin;
cmbzxf8u80007voyd1y17b1w2	cmbzxc3jf0005vopkptwzysem	cmbzxc2j20000vopkwj4t1gh7	2025-06-17 02:51:10.592	cmbzxc3jf0005vopkptwzysem	\N
cmbzxf9bh000avoydsqeizl01	cmbzxf93l0008voydmmjpd0nx	cmbzxc2sk0001vopkj3h6y229	2025-06-17 02:51:11.213	cmbzxc3jf0005vopkptwzysem	\N
cmbzxf9n2000dvoydt7irhmz4	cmbzxf9ha000bvoyduleboh82	cmbzxc2sk0001vopkj3h6y229	2025-06-17 02:51:11.63	cmbzxc3jf0005vopkptwzysem	\N
cmbzxf9z2000gvoyd42c4xonm	cmbzxf9t0000evoydmhh563od	cmbzxc2yd0002vopkesh6wc7l	2025-06-17 02:51:12.062	cmbzxc3jf0005vopkptwzysem	\N
cmbzxfaal000jvoyd4fyxv7ox	cmbzxfa4s000hvoydl623q6wo	cmbzxc2yd0002vopkesh6wc7l	2025-06-17 02:51:12.477	cmbzxc3jf0005vopkptwzysem	\N
cmbzxfanj000mvoyd92la3lmb	cmbzxfagj000kvoydwacf7ft2	cmbzxc3470003vopkbonw8fy1	2025-06-17 02:51:12.943	cmbzxc3jf0005vopkptwzysem	\N
cmbzxfb8o000rvoydo527flg2	cmbzxfb2x000pvoydpfuwqi4z	cmbzxc3470003vopkbonw8fy1	2025-06-17 02:51:13.704	cmbzxc3jf0005vopkptwzysem	\N
cmbzxfbqk000wvoydxm3vtlq0	cmbzxfbkm000uvoydavguvo47	cmbzxc3470003vopkbonw8fy1	2025-06-17 02:51:14.349	cmbzxc3jf0005vopkptwzysem	\N
\.


--
-- Data for Name: Vendor; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."Vendor" (id, "userId", "companyName", "companyType", "businessLicense", "taxId", address, city, province, "postalCode", country, "contactPerson", "contactPhone", "contactEmail", website, "businessCategory", "businessDescription", "establishedYear", "employeeCount", "annualRevenue", status, "verifiedAt", "verificationNotes", "createdAt", "updatedAt", "isLocked", "unlockExpiryDate", "picName", "verificationStatus") FROM stdin;
cmbzxfatj000ovoyd7s3sl61h	cmbzxfagj000kvoydwacf7ft2	PT. Technology Corporation	PT	123456789012345	01.234.567.8-901.000	Jl. Sudirman No. 123	Jakarta	DKI Jakarta	12345	Indonesia	John Doe	+62-21-5551234	<EMAIL>	\N	Information Technology	Software development and IT consulting services	2010	150	***********	VERIFIED	2025-06-17 02:51:12.688	\N	2025-06-17 02:51:13.158	2025-06-17 02:51:13.158	t	\N	John Doe	\N
cmbzxfbeh000tvoydtatxqtt6	cmbzxfb2x000pvoydpfuwqi4z	CV. Build Pro Construction	CV	234567890123456	02.345.678.9-012.000	Jl. Gatot Subroto No. 456	Bandung	Jawa Barat	40123	Indonesia	Jane Smith	+62-22-5551234	<EMAIL>	\N	Construction	Building construction and infrastructure development	2005	75	***********	VERIFIED	2025-06-17 02:51:12.688	\N	2025-06-17 02:51:13.913	2025-06-17 02:51:13.913	t	\N	Jane Smith	\N
cmbzxfbwc000yvoyd9pmlqyrb	cmbzxfbkm000uvoydavguvo47	PT. Supply Corporation	PT	345678901234567	03.456.789.0-123.000	Jl. Thamrin No. 789	Surabaya	Jawa Timur	60123	Indonesia	Bob Johnson	+62-31-5551234	<EMAIL>	\N	Office Supplies	Office equipment and supplies distribution	2015	50	15000000000	PENDING_VERIFICATION	\N	\N	2025-06-17 02:51:14.557	2025-06-17 02:51:14.557	t	\N	Bob Johnson	\N
\.


--
-- Data for Name: VendorEvaluation; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."VendorEvaluation" (id, "procurementId", "vendorId", score, notes, "evaluatedAt", "evaluatedById") FROM stdin;
\.


--
-- Data for Name: VendorIssue; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."VendorIssue" (id, "vendorId", title, description, category, severity, status, "reportedById", "assignedToId", "reportedAt", "resolvedAt", "closedAt", resolution, "resolutionTime", "procurementId", "purchaseOrderId", metadata, "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: VendorKpi; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."VendorKpi" (id, "vendorId", "procurementId", period, "qualityScore", "timeScore", "costScore", "serviceScore", "overallScore", remarks, "createdAt") FROM stdin;
\.


--
-- Data for Name: VendorKpiEvaluation; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."VendorKpiEvaluation" (id, "vendorId", "templateId", "evaluationPeriod", "startDate", "endDate", "overallScore", "categoryScores", "metricScores", rating, strengths, weaknesses, recommendations, status, "approvedAt", "approvedById", "createdAt", "updatedAt", "evaluatedById") FROM stdin;
\.


--
-- Data for Name: VendorKpiMetric; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."VendorKpiMetric" (id, "vendorId", "metricName", "metricType", category, "targetValue", "actualValue", "achievementRate", score, weight, "evaluationPeriod", "recordedDate", "sourceType", "sourceId", notes, "isAutoCalculated", "createdAt", "recordedById") FROM stdin;
\.


--
-- Data for Name: VendorKpiScore; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."VendorKpiScore" (id, "evaluationId", "metricId", score, "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: VendorKpiTemplate; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."VendorKpiTemplate" (id, name, description, category, "isActive", metrics, weights, thresholds, "calculationMethod", "evaluationPeriod", "createdAt", "updatedAt", "createdById") FROM stdin;
\.


--
-- Data for Name: VendorOffer; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."VendorOffer" (id, "procurementId", "vendorId", "totalPrice", currency, "validUntil", notes, status, "submittedAt", "adminScore", "evaluatedAt", "evaluatedBy", "evaluationComments", "negotiatedPrice", "negotiationComments", "negotiationNotes", "priceScore", "submissionDate", "techScore", "totalAmount") FROM stdin;
\.


--
-- Data for Name: VendorOfferItem; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."VendorOfferItem" (id, "offerId", "itemId", price, "correctedPrice", "correctionReason", "correctedAt", "correctedById") FROM stdin;
\.


--
-- Data for Name: VendorPerformanceHistory; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."VendorPerformanceHistory" (id, "vendorId", period, "overallScore", "qualityScore", "deliveryScore", "costScore", "serviceScore", "complianceScore", rank, "totalVendors", "previousScore", "scoreChange", trend, "createdAt") FROM stdin;
\.


--
-- Data for Name: VendorRequirementTemplate; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."VendorRequirementTemplate" (id, "templateId", name, description, category, type, criteria, weight, "validationRules", "requiredDocuments", "isActive", "createdAt", "updatedAt", "createdBy") FROM stdin;
cmc0555dr001kvonrh5vapca0	cmc05558s001evonrn4ci8jnd	IT Vendor Certification	Required certifications for IT vendors	TECHNICAL	MANDATORY	{"minScore": 80, "certifications": ["ISO 27001", "ISO 9001"]}	\N	{"certifications": ["ISO 27001", "ISO 9001"]}	\N	t	2025-06-17 06:27:16.479	2025-06-17 06:27:16.479	cmbzxc3jf0005vopkptwzysem
cmc0555g4001mvonr8n0gcba8	cmc0555bc001gvonrr7l8d6od	Construction License	Valid construction business license	LEGAL	MANDATORY	{"validUntil": "required", "licenseTypes": ["SIUJK", "SBU"]}	\N	{"licenseTypes": ["SIUJK", "SBU"]}	\N	t	2025-06-17 06:27:16.564	2025-06-17 06:27:16.564	cmbzxc3jf0005vopkptwzysem
cmc0ecxun001kvo5nkgd6k8y8	cmc0ecxof001evo5nlfnzj7vh	IT Vendor Certification	Required certifications for IT vendors	TECHNICAL	MANDATORY	{"minScore": 80, "certifications": ["ISO 27001", "ISO 9001"]}	\N	{"certifications": ["ISO 27001", "ISO 9001"]}	\N	t	2025-06-17 10:45:16.511	2025-06-17 10:45:16.511	cmbzxc3jf0005vopkptwzysem
cmc0ecxyj001mvo5n82z65ly2	cmc0ecxrn001gvo5n6zql32jk	Construction License	Valid construction business license	LEGAL	MANDATORY	{"validUntil": "required", "licenseTypes": ["SIUJK", "SBU"]}	\N	{"licenseTypes": ["SIUJK", "SBU"]}	\N	t	2025-06-17 10:45:16.651	2025-06-17 10:45:16.651	cmbzxc3jf0005vopkptwzysem
\.


--
-- Data for Name: VendorVerificationStep; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public."VendorVerificationStep" (id, "vendorId", "stepName", status, "verifiedById", "verifiedAt", comments) FROM stdin;
\.


--
-- Data for Name: _prisma_migrations; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public._prisma_migrations (id, checksum, finished_at, migration_name, logs, rolled_back_at, started_at, applied_steps_count) FROM stdin;
935278cc-bf33-43b1-900f-81902e029b6d	0a8d56335f896598250fbe7df4f2d176389bcfa10513080134a178b2dd3faab7	2025-06-17 02:48:39.500619+00	20250611232858_add_purchase_requisition_module	\N	\N	2025-06-17 02:48:38.621455+00	1
9a436839-7093-44ea-a166-171621da0b3a	3a8995f58d56a3e6bd636337831ae89d21fa2f4f4f2ae14f672b41840728ca0d	2025-06-17 02:48:39.816744+00	20250612075004_add_approval_delegation	\N	\N	2025-06-17 02:48:39.585439+00	1
\.


--
-- Data for Name: about_page_content; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public.about_page_content (id, "heroTitle", "heroSubtitle", "heroDescription", "aboutTitle", "aboutDescription", "companyTitle", "companyDescription", "companyName", "companyFullName", "operationalArea", vision, features, "contactTitle", "contactDescription", address, phones, emails, "ctaTitle", "ctaDescription", "createdAt", "updatedAt", "updatedById") FROM stdin;
cmbzxpw1g0001vocpwmge8pfy	Tentang E-Procurement	PT Bank BPD Sulteng	Sistem pengadaan elektronik yang mendukung transparansi, efisiensi, dan akuntabilitas dalam proses pengadaan barang dan jasa	Apa itu E-Procurement?	E-Procurement adalah sistem pengadaan elektronik yang memungkinkan proses pengadaan barang dan jasa dilakukan secara digital, transparan, dan efisien.	Tentang PT Bank BPD Sulteng	Bank Pembangunan Daerah Sulawesi Tengah yang berkomitmen pada pelayanan terbaik	PT Bank BPD Sulteng	Bank Pembangunan Daerah Sulawesi Tengah	Sulawesi Tengah dan sekitarnya	Menjadi bank pilihan utama yang mendukung pembangunan ekonomi daerah	["Proses tender yang transparan dan fair", "Dokumentasi digital yang lengkap", "Evaluasi vendor yang objektif", "Monitoring real-time status pengadaan", "Integrasi dengan sistem keuangan", "Audit trail yang komprehensif"]	Informasi Kontak	Hubungi kami untuk informasi lebih lanjut tentang e-procurement	Jl. Contoh No. 123\nPalu, Sulawesi Tengah\n94111	["+62 **********", "+62 **********"]	["<EMAIL>", "<EMAIL>"]	Siap Bermitra dengan Kami?	Bergabunglah dengan vendor-vendor terpercaya lainnya dalam sistem e-procurement kami	2025-06-17 02:59:27.22	2025-06-17 02:59:27.22	cmbzxc3jf0005vopkptwzysem
\.


--
-- Data for Name: audit_logs; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public.audit_logs (id, "userId", "userEmail", "userRole", action, resource, "resourceId", "oldValues", "newValues", "changedFields", "ipAddress", "userAgent", "sessionId", "requestId", "procurementId", "workflowStage", "approvalStep", severity, category, metadata, description, "timestamp", "partitionDate", "createdAt", details, "entityId", "entityType") FROM stdin;
\.


--
-- Data for Name: audit_logs_archive; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public.audit_logs_archive (id, "originalId", "userId", "userEmail", "userRole", action, resource, "resourceId", "oldValues", "newValues", "changedFields", "ipAddress", "userAgent", "sessionId", "requestId", "procurementId", "workflowStage", "approvalStep", severity, category, metadata, description, "timestamp", "archivedAt", "archiveReason", details, "entityId", "entityType", "partitionDate") FROM stdin;
\.


--
-- Data for Name: db_performance_logs; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public.db_performance_logs (id, operation, "tableName", "queryHash", "executionTime", "rowsAffected", "timestamp", "partitionDate") FROM stdin;
\.


--
-- Data for Name: document_template_versions; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public.document_template_versions (id, "templateId", version, content, variables, changelog, "createdBy", "createdAt") FROM stdin;
\.


--
-- Data for Name: document_templates; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public.document_templates (id, name, description, type, category, content, variables, version, status, "isActive", "createdBy", "updatedBy", "approvedBy", "approvedAt", "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: email_queue; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public.email_queue (id, "to", cc, bcc, subject, body, "htmlBody", attachments, "templateId", template, "templateData", priority, status, attempts, "maxAttempts", "retryCount", "maxRetries", "scheduledAt", "processedAt", "sentAt", "failedAt", error, metadata, "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: generated_documents; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public.generated_documents (id, "templateId", name, data, content, "pdfUrl", "createdBy", "createdAt", "documentType", "entityId", "entityType", "generatedAt", metadata, status, "templateName", version) FROM stdin;
\.


--
-- Data for Name: migrations; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public.migrations (id, "timestamp", name) FROM stdin;
\.


--
-- Data for Name: notifications; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public.notifications (id, "userId", title, message, type, "notificationType", read, metadata, "createdAt") FROM stdin;
\.


--
-- Data for Name: public_assets; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public.public_assets (id, "fileName", "originalFileName", "filePath", "fileSize", size, "mimeType", "fileType", category, type, "securityLevel", url, title, description, tags, "isActive", "isPublic", "allowDownload", version, "parentId", "publishedAt", "expiresAt", "archivedAt", "downloadCount", "viewCount", "lastAccessedAt", "uploadedById", metadata, "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: push_queue; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public.push_queue (id, "userId", title, body, data, badge, sound, priority, status, attempts, "maxAttempts", "scheduledAt", "processedAt", "failedAt", error, metadata, "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: sms_queue; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public.sms_queue (id, "to", message, "templateId", "templateData", priority, status, attempts, "maxAttempts", "scheduledAt", "processedAt", "failedAt", error, metadata, "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: webhook_queue; Type: TABLE DATA; Schema: public; Owner: avnadmin
--

COPY public.webhook_queue (id, url, method, headers, body, payload, "templateId", priority, status, attempts, "maxAttempts", "scheduledAt", "processedAt", "failedAt", error, metadata, "createdAt", "updatedAt") FROM stdin;
\.


--
-- Name: migrations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: avnadmin
--

SELECT pg_catalog.setval('public.migrations_id_seq', 1, false);


--
-- Name: ApprovalAction ApprovalAction_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ApprovalAction"
    ADD CONSTRAINT "ApprovalAction_pkey" PRIMARY KEY (id);


--
-- Name: ApprovalComment ApprovalComment_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ApprovalComment"
    ADD CONSTRAINT "ApprovalComment_pkey" PRIMARY KEY (id);


--
-- Name: ApprovalDelegation ApprovalDelegation_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ApprovalDelegation"
    ADD CONSTRAINT "ApprovalDelegation_pkey" PRIMARY KEY (id);


--
-- Name: ApprovalInstance ApprovalInstance_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ApprovalInstance"
    ADD CONSTRAINT "ApprovalInstance_pkey" PRIMARY KEY (id);


--
-- Name: ApprovalStepInstance ApprovalStepInstance_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ApprovalStepInstance"
    ADD CONSTRAINT "ApprovalStepInstance_pkey" PRIMARY KEY (id);


--
-- Name: ApprovalStep ApprovalStep_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ApprovalStep"
    ADD CONSTRAINT "ApprovalStep_pkey" PRIMARY KEY (id);


--
-- Name: ApprovalWorkflowConfig ApprovalWorkflowConfig_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ApprovalWorkflowConfig"
    ADD CONSTRAINT "ApprovalWorkflowConfig_pkey" PRIMARY KEY (id);


--
-- Name: ApprovalWorkflow ApprovalWorkflow_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ApprovalWorkflow"
    ADD CONSTRAINT "ApprovalWorkflow_pkey" PRIMARY KEY (id);


--
-- Name: Approval Approval_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."Approval"
    ADD CONSTRAINT "Approval_pkey" PRIMARY KEY (id);


--
-- Name: BAST BAST_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."BAST"
    ADD CONSTRAINT "BAST_pkey" PRIMARY KEY (id);


--
-- Name: BastChecklistItem BastChecklistItem_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."BastChecklistItem"
    ADD CONSTRAINT "BastChecklistItem_pkey" PRIMARY KEY (id);


--
-- Name: BlacklistEntry BlacklistEntry_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."BlacklistEntry"
    ADD CONSTRAINT "BlacklistEntry_pkey" PRIMARY KEY (id);


--
-- Name: ContentApproval ContentApproval_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ContentApproval"
    ADD CONSTRAINT "ContentApproval_pkey" PRIMARY KEY (id);


--
-- Name: Content Content_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."Content"
    ADD CONSTRAINT "Content_pkey" PRIMARY KEY (id);


--
-- Name: Contract Contract_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."Contract"
    ADD CONSTRAINT "Contract_pkey" PRIMARY KEY (id);


--
-- Name: DeliveryItem DeliveryItem_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."DeliveryItem"
    ADD CONSTRAINT "DeliveryItem_pkey" PRIMARY KEY (id);


--
-- Name: Delivery Delivery_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."Delivery"
    ADD CONSTRAINT "Delivery_pkey" PRIMARY KEY (id);


--
-- Name: Department Department_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."Department"
    ADD CONSTRAINT "Department_pkey" PRIMARY KEY (id);


--
-- Name: DiscussionAttachment DiscussionAttachment_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."DiscussionAttachment"
    ADD CONSTRAINT "DiscussionAttachment_pkey" PRIMARY KEY (id);


--
-- Name: DiscussionMessage DiscussionMessage_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."DiscussionMessage"
    ADD CONSTRAINT "DiscussionMessage_pkey" PRIMARY KEY (id);


--
-- Name: DiscussionParticipant DiscussionParticipant_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."DiscussionParticipant"
    ADD CONSTRAINT "DiscussionParticipant_pkey" PRIMARY KEY (id);


--
-- Name: DiscussionThread DiscussionThread_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."DiscussionThread"
    ADD CONSTRAINT "DiscussionThread_pkey" PRIMARY KEY (id);


--
-- Name: Document Document_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."Document"
    ADD CONSTRAINT "Document_pkey" PRIMARY KEY (id);


--
-- Name: EvaluationTemplate EvaluationTemplate_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."EvaluationTemplate"
    ADD CONSTRAINT "EvaluationTemplate_pkey" PRIMARY KEY (id);


--
-- Name: GoodReceiptItem GoodReceiptItem_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."GoodReceiptItem"
    ADD CONSTRAINT "GoodReceiptItem_pkey" PRIMARY KEY (id);


--
-- Name: GoodReceipt GoodReceipt_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."GoodReceipt"
    ADD CONSTRAINT "GoodReceipt_pkey" PRIMARY KEY (id);


--
-- Name: GoodsReceiptNote GoodsReceiptNote_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."GoodsReceiptNote"
    ADD CONSTRAINT "GoodsReceiptNote_pkey" PRIMARY KEY (id);


--
-- Name: Invoice Invoice_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."Invoice"
    ADD CONSTRAINT "Invoice_pkey" PRIMARY KEY (id);


--
-- Name: ItemMaster ItemMaster_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ItemMaster"
    ADD CONSTRAINT "ItemMaster_pkey" PRIMARY KEY (id);


--
-- Name: MessageAttachment MessageAttachment_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."MessageAttachment"
    ADD CONSTRAINT "MessageAttachment_pkey" PRIMARY KEY (id);


--
-- Name: MessageReaction MessageReaction_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."MessageReaction"
    ADD CONSTRAINT "MessageReaction_pkey" PRIMARY KEY (id);


--
-- Name: NegotiationRound NegotiationRound_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."NegotiationRound"
    ADD CONSTRAINT "NegotiationRound_pkey" PRIMARY KEY (id);


--
-- Name: NegotiationSession NegotiationSession_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."NegotiationSession"
    ADD CONSTRAINT "NegotiationSession_pkey" PRIMARY KEY (id);


--
-- Name: NewsArticle NewsArticle_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."NewsArticle"
    ADD CONSTRAINT "NewsArticle_pkey" PRIMARY KEY (id);


--
-- Name: NewsCategory NewsCategory_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."NewsCategory"
    ADD CONSTRAINT "NewsCategory_pkey" PRIMARY KEY (id);


--
-- Name: NotificationEscalation NotificationEscalation_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."NotificationEscalation"
    ADD CONSTRAINT "NotificationEscalation_pkey" PRIMARY KEY (id);


--
-- Name: NotificationTemplate NotificationTemplate_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."NotificationTemplate"
    ADD CONSTRAINT "NotificationTemplate_pkey" PRIMARY KEY (id);


--
-- Name: OfferEvaluation OfferEvaluation_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."OfferEvaluation"
    ADD CONSTRAINT "OfferEvaluation_pkey" PRIMARY KEY (id);


--
-- Name: Offer Offer_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."Offer"
    ADD CONSTRAINT "Offer_pkey" PRIMARY KEY (id);


--
-- Name: migrations PK_8c82d7f526340ab734260ea46be; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public.migrations
    ADD CONSTRAINT "PK_8c82d7f526340ab734260ea46be" PRIMARY KEY (id);


--
-- Name: PriceCorrectionLog PriceCorrectionLog_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."PriceCorrectionLog"
    ADD CONSTRAINT "PriceCorrectionLog_pkey" PRIMARY KEY (id);


--
-- Name: ProcurementCommitteeMember ProcurementCommitteeMember_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ProcurementCommitteeMember"
    ADD CONSTRAINT "ProcurementCommitteeMember_pkey" PRIMARY KEY (id);


--
-- Name: ProcurementContent ProcurementContent_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ProcurementContent"
    ADD CONSTRAINT "ProcurementContent_pkey" PRIMARY KEY (id);


--
-- Name: ProcurementDiscussion ProcurementDiscussion_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ProcurementDiscussion"
    ADD CONSTRAINT "ProcurementDiscussion_pkey" PRIMARY KEY (id);


--
-- Name: ProcurementItemInclude ProcurementItemInclude_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ProcurementItemInclude"
    ADD CONSTRAINT "ProcurementItemInclude_pkey" PRIMARY KEY (id);


--
-- Name: ProcurementItem ProcurementItem_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ProcurementItem"
    ADD CONSTRAINT "ProcurementItem_pkey" PRIMARY KEY (id);


--
-- Name: ProcurementMilestone ProcurementMilestone_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ProcurementMilestone"
    ADD CONSTRAINT "ProcurementMilestone_pkey" PRIMARY KEY (id);


--
-- Name: ProcurementPackage ProcurementPackage_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ProcurementPackage"
    ADD CONSTRAINT "ProcurementPackage_pkey" PRIMARY KEY (id);


--
-- Name: ProcurementScheduleTemplate ProcurementScheduleTemplate_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ProcurementScheduleTemplate"
    ADD CONSTRAINT "ProcurementScheduleTemplate_pkey" PRIMARY KEY (id);


--
-- Name: ProcurementSchedule ProcurementSchedule_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ProcurementSchedule"
    ADD CONSTRAINT "ProcurementSchedule_pkey" PRIMARY KEY (id);


--
-- Name: ProcurementStage ProcurementStage_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ProcurementStage"
    ADD CONSTRAINT "ProcurementStage_pkey" PRIMARY KEY (id);


--
-- Name: ProcurementVendorRequirement ProcurementVendorRequirement_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ProcurementVendorRequirement"
    ADD CONSTRAINT "ProcurementVendorRequirement_pkey" PRIMARY KEY (id);


--
-- Name: ProcurementWorkflowStageTemplate ProcurementWorkflowStageTemplate_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ProcurementWorkflowStageTemplate"
    ADD CONSTRAINT "ProcurementWorkflowStageTemplate_pkey" PRIMARY KEY (id);


--
-- Name: ProcurementWorkflowTemplate ProcurementWorkflowTemplate_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ProcurementWorkflowTemplate"
    ADD CONSTRAINT "ProcurementWorkflowTemplate_pkey" PRIMARY KEY (id);


--
-- Name: Procurement Procurement_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."Procurement"
    ADD CONSTRAINT "Procurement_pkey" PRIMARY KEY (id);


--
-- Name: PurchaseOrderItem PurchaseOrderItem_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."PurchaseOrderItem"
    ADD CONSTRAINT "PurchaseOrderItem_pkey" PRIMARY KEY (id);


--
-- Name: PurchaseOrder PurchaseOrder_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."PurchaseOrder"
    ADD CONSTRAINT "PurchaseOrder_pkey" PRIMARY KEY (id);


--
-- Name: PurchaseRequisitionItem PurchaseRequisitionItem_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."PurchaseRequisitionItem"
    ADD CONSTRAINT "PurchaseRequisitionItem_pkey" PRIMARY KEY (id);


--
-- Name: PurchaseRequisition PurchaseRequisition_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."PurchaseRequisition"
    ADD CONSTRAINT "PurchaseRequisition_pkey" PRIMARY KEY (id);


--
-- Name: ReceiptLog ReceiptLog_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ReceiptLog"
    ADD CONSTRAINT "ReceiptLog_pkey" PRIMARY KEY (id);


--
-- Name: ResourcePermission ResourcePermission_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ResourcePermission"
    ADD CONSTRAINT "ResourcePermission_pkey" PRIMARY KEY (id);


--
-- Name: Role Role_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."Role"
    ADD CONSTRAINT "Role_pkey" PRIMARY KEY (id);


--
-- Name: SanctionedIndividual SanctionedIndividual_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."SanctionedIndividual"
    ADD CONSTRAINT "SanctionedIndividual_pkey" PRIMARY KEY (id);


--
-- Name: TaxExemption TaxExemption_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."TaxExemption"
    ADD CONSTRAINT "TaxExemption_pkey" PRIMARY KEY (id);


--
-- Name: TaxType TaxType_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."TaxType"
    ADD CONSTRAINT "TaxType_pkey" PRIMARY KEY (id);


--
-- Name: UserRole UserRole_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."UserRole"
    ADD CONSTRAINT "UserRole_pkey" PRIMARY KEY (id);


--
-- Name: User User_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."User"
    ADD CONSTRAINT "User_pkey" PRIMARY KEY (id);


--
-- Name: VendorEvaluation VendorEvaluation_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."VendorEvaluation"
    ADD CONSTRAINT "VendorEvaluation_pkey" PRIMARY KEY (id);


--
-- Name: VendorIssue VendorIssue_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."VendorIssue"
    ADD CONSTRAINT "VendorIssue_pkey" PRIMARY KEY (id);


--
-- Name: VendorKpiEvaluation VendorKpiEvaluation_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."VendorKpiEvaluation"
    ADD CONSTRAINT "VendorKpiEvaluation_pkey" PRIMARY KEY (id);


--
-- Name: VendorKpiMetric VendorKpiMetric_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."VendorKpiMetric"
    ADD CONSTRAINT "VendorKpiMetric_pkey" PRIMARY KEY (id);


--
-- Name: VendorKpiScore VendorKpiScore_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."VendorKpiScore"
    ADD CONSTRAINT "VendorKpiScore_pkey" PRIMARY KEY (id);


--
-- Name: VendorKpiTemplate VendorKpiTemplate_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."VendorKpiTemplate"
    ADD CONSTRAINT "VendorKpiTemplate_pkey" PRIMARY KEY (id);


--
-- Name: VendorKpi VendorKpi_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."VendorKpi"
    ADD CONSTRAINT "VendorKpi_pkey" PRIMARY KEY (id);


--
-- Name: VendorOfferItem VendorOfferItem_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."VendorOfferItem"
    ADD CONSTRAINT "VendorOfferItem_pkey" PRIMARY KEY (id);


--
-- Name: VendorOffer VendorOffer_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."VendorOffer"
    ADD CONSTRAINT "VendorOffer_pkey" PRIMARY KEY (id);


--
-- Name: VendorPerformanceHistory VendorPerformanceHistory_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."VendorPerformanceHistory"
    ADD CONSTRAINT "VendorPerformanceHistory_pkey" PRIMARY KEY (id);


--
-- Name: VendorRequirementTemplate VendorRequirementTemplate_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."VendorRequirementTemplate"
    ADD CONSTRAINT "VendorRequirementTemplate_pkey" PRIMARY KEY (id);


--
-- Name: VendorVerificationStep VendorVerificationStep_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."VendorVerificationStep"
    ADD CONSTRAINT "VendorVerificationStep_pkey" PRIMARY KEY (id);


--
-- Name: Vendor Vendor_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."Vendor"
    ADD CONSTRAINT "Vendor_pkey" PRIMARY KEY (id);


--
-- Name: _prisma_migrations _prisma_migrations_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public._prisma_migrations
    ADD CONSTRAINT _prisma_migrations_pkey PRIMARY KEY (id);


--
-- Name: about_page_content about_page_content_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public.about_page_content
    ADD CONSTRAINT about_page_content_pkey PRIMARY KEY (id);


--
-- Name: audit_logs_archive audit_logs_archive_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public.audit_logs_archive
    ADD CONSTRAINT audit_logs_archive_pkey PRIMARY KEY (id);


--
-- Name: audit_logs audit_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public.audit_logs
    ADD CONSTRAINT audit_logs_pkey PRIMARY KEY (id);


--
-- Name: db_performance_logs db_performance_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public.db_performance_logs
    ADD CONSTRAINT db_performance_logs_pkey PRIMARY KEY (id);


--
-- Name: document_template_versions document_template_versions_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public.document_template_versions
    ADD CONSTRAINT document_template_versions_pkey PRIMARY KEY (id);


--
-- Name: document_templates document_templates_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public.document_templates
    ADD CONSTRAINT document_templates_pkey PRIMARY KEY (id);


--
-- Name: email_queue email_queue_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public.email_queue
    ADD CONSTRAINT email_queue_pkey PRIMARY KEY (id);


--
-- Name: generated_documents generated_documents_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public.generated_documents
    ADD CONSTRAINT generated_documents_pkey PRIMARY KEY (id);


--
-- Name: notifications notifications_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public.notifications
    ADD CONSTRAINT notifications_pkey PRIMARY KEY (id);


--
-- Name: public_assets public_assets_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public.public_assets
    ADD CONSTRAINT public_assets_pkey PRIMARY KEY (id);


--
-- Name: push_queue push_queue_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public.push_queue
    ADD CONSTRAINT push_queue_pkey PRIMARY KEY (id);


--
-- Name: sms_queue sms_queue_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public.sms_queue
    ADD CONSTRAINT sms_queue_pkey PRIMARY KEY (id);


--
-- Name: webhook_queue webhook_queue_pkey; Type: CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public.webhook_queue
    ADD CONSTRAINT webhook_queue_pkey PRIMARY KEY (id);


--
-- Name: ApprovalAction_action_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "ApprovalAction_action_idx" ON public."ApprovalAction" USING btree (action);


--
-- Name: ApprovalAction_createdAt_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "ApprovalAction_createdAt_idx" ON public."ApprovalAction" USING btree ("createdAt");


--
-- Name: ApprovalAction_performedById_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "ApprovalAction_performedById_idx" ON public."ApprovalAction" USING btree ("performedById");


--
-- Name: ApprovalAction_stepInstanceId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "ApprovalAction_stepInstanceId_idx" ON public."ApprovalAction" USING btree ("stepInstanceId");


--
-- Name: ApprovalComment_authorId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "ApprovalComment_authorId_idx" ON public."ApprovalComment" USING btree ("authorId");


--
-- Name: ApprovalComment_createdAt_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "ApprovalComment_createdAt_idx" ON public."ApprovalComment" USING btree ("createdAt");


--
-- Name: ApprovalComment_instanceId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "ApprovalComment_instanceId_idx" ON public."ApprovalComment" USING btree ("instanceId");


--
-- Name: ApprovalDelegation_entityType_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "ApprovalDelegation_entityType_idx" ON public."ApprovalDelegation" USING btree ("entityType");


--
-- Name: ApprovalDelegation_fromUserId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "ApprovalDelegation_fromUserId_idx" ON public."ApprovalDelegation" USING btree ("fromUserId");


--
-- Name: ApprovalDelegation_isActive_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "ApprovalDelegation_isActive_idx" ON public."ApprovalDelegation" USING btree ("isActive");


--
-- Name: ApprovalDelegation_startDate_endDate_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "ApprovalDelegation_startDate_endDate_idx" ON public."ApprovalDelegation" USING btree ("startDate", "endDate");


--
-- Name: ApprovalDelegation_toUserId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "ApprovalDelegation_toUserId_idx" ON public."ApprovalDelegation" USING btree ("toUserId");


--
-- Name: ApprovalInstance_dueDate_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "ApprovalInstance_dueDate_idx" ON public."ApprovalInstance" USING btree ("dueDate");


--
-- Name: ApprovalInstance_entityType_entityId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "ApprovalInstance_entityType_entityId_idx" ON public."ApprovalInstance" USING btree ("entityType", "entityId");


--
-- Name: ApprovalInstance_initiatedById_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "ApprovalInstance_initiatedById_idx" ON public."ApprovalInstance" USING btree ("initiatedById");


--
-- Name: ApprovalInstance_purchaseRequisitionId_key; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE UNIQUE INDEX "ApprovalInstance_purchaseRequisitionId_key" ON public."ApprovalInstance" USING btree ("purchaseRequisitionId");


--
-- Name: ApprovalInstance_stage_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "ApprovalInstance_stage_idx" ON public."ApprovalInstance" USING btree (stage);


--
-- Name: ApprovalInstance_status_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "ApprovalInstance_status_idx" ON public."ApprovalInstance" USING btree (status);


--
-- Name: ApprovalStepInstance_instanceId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "ApprovalStepInstance_instanceId_idx" ON public."ApprovalStepInstance" USING btree ("instanceId");


--
-- Name: ApprovalStepInstance_sequence_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "ApprovalStepInstance_sequence_idx" ON public."ApprovalStepInstance" USING btree (sequence);


--
-- Name: ApprovalStepInstance_status_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "ApprovalStepInstance_status_idx" ON public."ApprovalStepInstance" USING btree (status);


--
-- Name: ApprovalStepInstance_stepId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "ApprovalStepInstance_stepId_idx" ON public."ApprovalStepInstance" USING btree ("stepId");


--
-- Name: ApprovalStep_sequence_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "ApprovalStep_sequence_idx" ON public."ApprovalStep" USING btree (sequence);


--
-- Name: ApprovalStep_workflowId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "ApprovalStep_workflowId_idx" ON public."ApprovalStep" USING btree ("workflowId");


--
-- Name: ApprovalStep_workflowId_sequence_key; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE UNIQUE INDEX "ApprovalStep_workflowId_sequence_key" ON public."ApprovalStep" USING btree ("workflowId", sequence);


--
-- Name: ApprovalWorkflowConfig_entityType_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "ApprovalWorkflowConfig_entityType_idx" ON public."ApprovalWorkflowConfig" USING btree ("entityType");


--
-- Name: ApprovalWorkflowConfig_isActive_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "ApprovalWorkflowConfig_isActive_idx" ON public."ApprovalWorkflowConfig" USING btree ("isActive");


--
-- Name: ApprovalWorkflow_entityType_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "ApprovalWorkflow_entityType_idx" ON public."ApprovalWorkflow" USING btree ("entityType");


--
-- Name: ApprovalWorkflow_isActive_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "ApprovalWorkflow_isActive_idx" ON public."ApprovalWorkflow" USING btree ("isActive");


--
-- Name: ApprovalWorkflow_isDefault_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "ApprovalWorkflow_isDefault_idx" ON public."ApprovalWorkflow" USING btree ("isDefault");


--
-- Name: ApprovalWorkflow_stage_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "ApprovalWorkflow_stage_idx" ON public."ApprovalWorkflow" USING btree (stage);


--
-- Name: Approval_approverId_status_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "Approval_approverId_status_idx" ON public."Approval" USING btree ("approverId", status);


--
-- Name: BAST_bastNumber_key; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE UNIQUE INDEX "BAST_bastNumber_key" ON public."BAST" USING btree ("bastNumber");


--
-- Name: BAST_createdAt_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "BAST_createdAt_idx" ON public."BAST" USING btree ("createdAt");


--
-- Name: BAST_poId_status_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "BAST_poId_status_idx" ON public."BAST" USING btree ("poId", status);


--
-- Name: BlacklistEntry_endDate_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "BlacklistEntry_endDate_idx" ON public."BlacklistEntry" USING btree ("endDate");


--
-- Name: BlacklistEntry_severity_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "BlacklistEntry_severity_idx" ON public."BlacklistEntry" USING btree (severity);


--
-- Name: BlacklistEntry_startDate_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "BlacklistEntry_startDate_idx" ON public."BlacklistEntry" USING btree ("startDate");


--
-- Name: BlacklistEntry_status_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "BlacklistEntry_status_idx" ON public."BlacklistEntry" USING btree (status);


--
-- Name: BlacklistEntry_vendorId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "BlacklistEntry_vendorId_idx" ON public."BlacklistEntry" USING btree ("vendorId");


--
-- Name: ContentApproval_contentType_contentId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "ContentApproval_contentType_contentId_idx" ON public."ContentApproval" USING btree ("contentType", "contentId");


--
-- Name: ContentApproval_requestedById_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "ContentApproval_requestedById_idx" ON public."ContentApproval" USING btree ("requestedById");


--
-- Name: ContentApproval_reviewedById_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "ContentApproval_reviewedById_idx" ON public."ContentApproval" USING btree ("reviewedById");


--
-- Name: ContentApproval_status_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "ContentApproval_status_idx" ON public."ContentApproval" USING btree (status);


--
-- Name: Content_authorId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "Content_authorId_idx" ON public."Content" USING btree ("authorId");


--
-- Name: Content_publishedAt_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "Content_publishedAt_idx" ON public."Content" USING btree ("publishedAt");


--
-- Name: Content_slug_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "Content_slug_idx" ON public."Content" USING btree (slug);


--
-- Name: Content_slug_type_key; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE UNIQUE INDEX "Content_slug_type_key" ON public."Content" USING btree (slug, type);


--
-- Name: Content_status_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "Content_status_idx" ON public."Content" USING btree (status);


--
-- Name: Content_type_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "Content_type_idx" ON public."Content" USING btree (type);


--
-- Name: Contract_contractNumber_key; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE UNIQUE INDEX "Contract_contractNumber_key" ON public."Contract" USING btree ("contractNumber");


--
-- Name: DeliveryItem_deliveryId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "DeliveryItem_deliveryId_idx" ON public."DeliveryItem" USING btree ("deliveryId");


--
-- Name: DeliveryItem_poItemId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "DeliveryItem_poItemId_idx" ON public."DeliveryItem" USING btree ("poItemId");


--
-- Name: Delivery_deliveryDate_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "Delivery_deliveryDate_idx" ON public."Delivery" USING btree ("deliveryDate");


--
-- Name: Delivery_deliveryNumber_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "Delivery_deliveryNumber_idx" ON public."Delivery" USING btree ("deliveryNumber");


--
-- Name: Delivery_deliveryNumber_key; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE UNIQUE INDEX "Delivery_deliveryNumber_key" ON public."Delivery" USING btree ("deliveryNumber");


--
-- Name: Delivery_poId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "Delivery_poId_idx" ON public."Delivery" USING btree ("poId");


--
-- Name: Delivery_status_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "Delivery_status_idx" ON public."Delivery" USING btree (status);


--
-- Name: Department_name_key; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE UNIQUE INDEX "Department_name_key" ON public."Department" USING btree (name);


--
-- Name: DiscussionAttachment_discussionId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "DiscussionAttachment_discussionId_idx" ON public."DiscussionAttachment" USING btree ("discussionId");


--
-- Name: DiscussionAttachment_uploadedById_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "DiscussionAttachment_uploadedById_idx" ON public."DiscussionAttachment" USING btree ("uploadedById");


--
-- Name: DiscussionMessage_authorId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "DiscussionMessage_authorId_idx" ON public."DiscussionMessage" USING btree ("authorId");


--
-- Name: DiscussionMessage_createdAt_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "DiscussionMessage_createdAt_idx" ON public."DiscussionMessage" USING btree ("createdAt");


--
-- Name: DiscussionMessage_discussionId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "DiscussionMessage_discussionId_idx" ON public."DiscussionMessage" USING btree ("discussionId");


--
-- Name: DiscussionMessage_parentId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "DiscussionMessage_parentId_idx" ON public."DiscussionMessage" USING btree ("parentId");


--
-- Name: DiscussionParticipant_discussionId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "DiscussionParticipant_discussionId_idx" ON public."DiscussionParticipant" USING btree ("discussionId");


--
-- Name: DiscussionParticipant_discussionId_userId_key; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE UNIQUE INDEX "DiscussionParticipant_discussionId_userId_key" ON public."DiscussionParticipant" USING btree ("discussionId", "userId");


--
-- Name: DiscussionParticipant_userId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "DiscussionParticipant_userId_idx" ON public."DiscussionParticipant" USING btree ("userId");


--
-- Name: DiscussionThread_procurementStageId_key; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE UNIQUE INDEX "DiscussionThread_procurementStageId_key" ON public."DiscussionThread" USING btree ("procurementStageId");


--
-- Name: Document_fileUrl_key; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE UNIQUE INDEX "Document_fileUrl_key" ON public."Document" USING btree ("fileUrl");


--
-- Name: EvaluationTemplate_name_key; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE UNIQUE INDEX "EvaluationTemplate_name_key" ON public."EvaluationTemplate" USING btree (name);


--
-- Name: GoodReceiptItem_grnId_purchaseOrderItemId_key; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE UNIQUE INDEX "GoodReceiptItem_grnId_purchaseOrderItemId_key" ON public."GoodReceiptItem" USING btree ("grnId", "purchaseOrderItemId");


--
-- Name: GoodReceipt_grNumber_key; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE UNIQUE INDEX "GoodReceipt_grNumber_key" ON public."GoodReceipt" USING btree ("grNumber");


--
-- Name: GoodsReceiptNote_grnNumber_key; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE UNIQUE INDEX "GoodsReceiptNote_grnNumber_key" ON public."GoodsReceiptNote" USING btree ("grnNumber");


--
-- Name: GoodsReceiptNote_poId_key; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE UNIQUE INDEX "GoodsReceiptNote_poId_key" ON public."GoodsReceiptNote" USING btree ("poId");


--
-- Name: Invoice_invoiceNumber_key; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE UNIQUE INDEX "Invoice_invoiceNumber_key" ON public."Invoice" USING btree ("invoiceNumber");


--
-- Name: Invoice_poId_status_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "Invoice_poId_status_idx" ON public."Invoice" USING btree ("poId", status);


--
-- Name: ItemMaster_category_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "ItemMaster_category_idx" ON public."ItemMaster" USING btree (category);


--
-- Name: ItemMaster_itemCode_key; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE UNIQUE INDEX "ItemMaster_itemCode_key" ON public."ItemMaster" USING btree ("itemCode");


--
-- Name: MessageAttachment_messageId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "MessageAttachment_messageId_idx" ON public."MessageAttachment" USING btree ("messageId");


--
-- Name: MessageAttachment_uploadedById_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "MessageAttachment_uploadedById_idx" ON public."MessageAttachment" USING btree ("uploadedById");


--
-- Name: MessageReaction_messageId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "MessageReaction_messageId_idx" ON public."MessageReaction" USING btree ("messageId");


--
-- Name: MessageReaction_messageId_userId_emoji_key; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE UNIQUE INDEX "MessageReaction_messageId_userId_emoji_key" ON public."MessageReaction" USING btree ("messageId", "userId", emoji);


--
-- Name: MessageReaction_userId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "MessageReaction_userId_idx" ON public."MessageReaction" USING btree ("userId");


--
-- Name: NegotiationRound_roundNumber_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "NegotiationRound_roundNumber_idx" ON public."NegotiationRound" USING btree ("roundNumber");


--
-- Name: NegotiationRound_sessionId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "NegotiationRound_sessionId_idx" ON public."NegotiationRound" USING btree ("sessionId");


--
-- Name: NegotiationSession_procurementId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "NegotiationSession_procurementId_idx" ON public."NegotiationSession" USING btree ("procurementId");


--
-- Name: NegotiationSession_status_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "NegotiationSession_status_idx" ON public."NegotiationSession" USING btree (status);


--
-- Name: NegotiationSession_vendorId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "NegotiationSession_vendorId_idx" ON public."NegotiationSession" USING btree ("vendorId");


--
-- Name: NewsArticle_authorId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "NewsArticle_authorId_idx" ON public."NewsArticle" USING btree ("authorId");


--
-- Name: NewsArticle_categoryId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "NewsArticle_categoryId_idx" ON public."NewsArticle" USING btree ("categoryId");


--
-- Name: NewsArticle_publishedAt_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "NewsArticle_publishedAt_idx" ON public."NewsArticle" USING btree ("publishedAt");


--
-- Name: NewsArticle_slug_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "NewsArticle_slug_idx" ON public."NewsArticle" USING btree (slug);


--
-- Name: NewsArticle_slug_key; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE UNIQUE INDEX "NewsArticle_slug_key" ON public."NewsArticle" USING btree (slug);


--
-- Name: NewsArticle_status_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "NewsArticle_status_idx" ON public."NewsArticle" USING btree (status);


--
-- Name: NewsCategory_isActive_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "NewsCategory_isActive_idx" ON public."NewsCategory" USING btree ("isActive");


--
-- Name: NewsCategory_slug_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "NewsCategory_slug_idx" ON public."NewsCategory" USING btree (slug);


--
-- Name: NewsCategory_slug_key; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE UNIQUE INDEX "NewsCategory_slug_key" ON public."NewsCategory" USING btree (slug);


--
-- Name: NotificationEscalation_entityType_entityId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "NotificationEscalation_entityType_entityId_idx" ON public."NotificationEscalation" USING btree ("entityType", "entityId");


--
-- Name: NotificationEscalation_escalateAt_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "NotificationEscalation_escalateAt_idx" ON public."NotificationEscalation" USING btree ("escalateAt");


--
-- Name: NotificationEscalation_status_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "NotificationEscalation_status_idx" ON public."NotificationEscalation" USING btree (status);


--
-- Name: NotificationTemplate_category_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "NotificationTemplate_category_idx" ON public."NotificationTemplate" USING btree (category);


--
-- Name: NotificationTemplate_isActive_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "NotificationTemplate_isActive_idx" ON public."NotificationTemplate" USING btree ("isActive");


--
-- Name: NotificationTemplate_name_key; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE UNIQUE INDEX "NotificationTemplate_name_key" ON public."NotificationTemplate" USING btree (name);


--
-- Name: OfferEvaluation_evaluatedById_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "OfferEvaluation_evaluatedById_idx" ON public."OfferEvaluation" USING btree ("evaluatedById");


--
-- Name: OfferEvaluation_offerId_evaluatedById_key; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE UNIQUE INDEX "OfferEvaluation_offerId_evaluatedById_key" ON public."OfferEvaluation" USING btree ("offerId", "evaluatedById");


--
-- Name: OfferEvaluation_offerId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "OfferEvaluation_offerId_idx" ON public."OfferEvaluation" USING btree ("offerId");


--
-- Name: OfferEvaluation_totalScore_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "OfferEvaluation_totalScore_idx" ON public."OfferEvaluation" USING btree ("totalScore");


--
-- Name: Offer_procurementId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "Offer_procurementId_idx" ON public."Offer" USING btree ("procurementId");


--
-- Name: Offer_procurementId_vendorId_key; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE UNIQUE INDEX "Offer_procurementId_vendorId_key" ON public."Offer" USING btree ("procurementId", "vendorId");


--
-- Name: Offer_vendorId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "Offer_vendorId_idx" ON public."Offer" USING btree ("vendorId");


--
-- Name: ProcurementCommitteeMember_procurementId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "ProcurementCommitteeMember_procurementId_idx" ON public."ProcurementCommitteeMember" USING btree ("procurementId");


--
-- Name: ProcurementCommitteeMember_procurementId_userId_key; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE UNIQUE INDEX "ProcurementCommitteeMember_procurementId_userId_key" ON public."ProcurementCommitteeMember" USING btree ("procurementId", "userId");


--
-- Name: ProcurementCommitteeMember_userId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "ProcurementCommitteeMember_userId_idx" ON public."ProcurementCommitteeMember" USING btree ("userId");


--
-- Name: ProcurementContent_authorId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "ProcurementContent_authorId_idx" ON public."ProcurementContent" USING btree ("authorId");


--
-- Name: ProcurementContent_publishedAt_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "ProcurementContent_publishedAt_idx" ON public."ProcurementContent" USING btree ("publishedAt");


--
-- Name: ProcurementContent_slug_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "ProcurementContent_slug_idx" ON public."ProcurementContent" USING btree (slug);


--
-- Name: ProcurementContent_slug_key; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE UNIQUE INDEX "ProcurementContent_slug_key" ON public."ProcurementContent" USING btree (slug);


--
-- Name: ProcurementContent_status_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "ProcurementContent_status_idx" ON public."ProcurementContent" USING btree (status);


--
-- Name: ProcurementContent_type_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "ProcurementContent_type_idx" ON public."ProcurementContent" USING btree (type);


--
-- Name: ProcurementDiscussion_createdById_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "ProcurementDiscussion_createdById_idx" ON public."ProcurementDiscussion" USING btree ("createdById");


--
-- Name: ProcurementDiscussion_procurementId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "ProcurementDiscussion_procurementId_idx" ON public."ProcurementDiscussion" USING btree ("procurementId");


--
-- Name: ProcurementDiscussion_status_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "ProcurementDiscussion_status_idx" ON public."ProcurementDiscussion" USING btree (status);


--
-- Name: ProcurementDiscussion_type_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "ProcurementDiscussion_type_idx" ON public."ProcurementDiscussion" USING btree (type);


--
-- Name: ProcurementItemInclude_itemId_key; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE UNIQUE INDEX "ProcurementItemInclude_itemId_key" ON public."ProcurementItemInclude" USING btree ("itemId");


--
-- Name: ProcurementItem_procurementId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "ProcurementItem_procurementId_idx" ON public."ProcurementItem" USING btree ("procurementId");


--
-- Name: ProcurementMilestone_plannedDate_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "ProcurementMilestone_plannedDate_idx" ON public."ProcurementMilestone" USING btree ("plannedDate");


--
-- Name: ProcurementMilestone_scheduleId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "ProcurementMilestone_scheduleId_idx" ON public."ProcurementMilestone" USING btree ("scheduleId");


--
-- Name: ProcurementPackage_packageNumber_key; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE UNIQUE INDEX "ProcurementPackage_packageNumber_key" ON public."ProcurementPackage" USING btree ("packageNumber");


--
-- Name: ProcurementPackage_procurementId_key; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE UNIQUE INDEX "ProcurementPackage_procurementId_key" ON public."ProcurementPackage" USING btree ("procurementId");


--
-- Name: ProcurementScheduleTemplate_templateId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "ProcurementScheduleTemplate_templateId_idx" ON public."ProcurementScheduleTemplate" USING btree ("templateId");


--
-- Name: ProcurementSchedule_procurementId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "ProcurementSchedule_procurementId_idx" ON public."ProcurementSchedule" USING btree ("procurementId");


--
-- Name: ProcurementSchedule_procurementId_key; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE UNIQUE INDEX "ProcurementSchedule_procurementId_key" ON public."ProcurementSchedule" USING btree ("procurementId");


--
-- Name: ProcurementStage_procurementId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "ProcurementStage_procurementId_idx" ON public."ProcurementStage" USING btree ("procurementId");


--
-- Name: ProcurementStage_procurementId_sequence_key; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE UNIQUE INDEX "ProcurementStage_procurementId_sequence_key" ON public."ProcurementStage" USING btree ("procurementId", sequence);


--
-- Name: ProcurementVendorRequirement_procurementId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "ProcurementVendorRequirement_procurementId_idx" ON public."ProcurementVendorRequirement" USING btree ("procurementId");


--
-- Name: ProcurementVendorRequirement_procurementId_requirementId_key; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE UNIQUE INDEX "ProcurementVendorRequirement_procurementId_requirementId_key" ON public."ProcurementVendorRequirement" USING btree ("procurementId", "requirementId");


--
-- Name: ProcurementWorkflowStageTemplate_templateId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "ProcurementWorkflowStageTemplate_templateId_idx" ON public."ProcurementWorkflowStageTemplate" USING btree ("templateId");


--
-- Name: ProcurementWorkflowStageTemplate_templateId_sequence_key; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE UNIQUE INDEX "ProcurementWorkflowStageTemplate_templateId_sequence_key" ON public."ProcurementWorkflowStageTemplate" USING btree ("templateId", sequence);


--
-- Name: ProcurementWorkflowTemplate_isDefault_isActive_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "ProcurementWorkflowTemplate_isDefault_isActive_idx" ON public."ProcurementWorkflowTemplate" USING btree ("isDefault", "isActive");


--
-- Name: ProcurementWorkflowTemplate_type_category_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "ProcurementWorkflowTemplate_type_category_idx" ON public."ProcurementWorkflowTemplate" USING btree (type, category);


--
-- Name: Procurement_category_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "Procurement_category_idx" ON public."Procurement" USING btree (category);


--
-- Name: Procurement_createdById_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "Procurement_createdById_idx" ON public."Procurement" USING btree ("createdById");


--
-- Name: Procurement_procurementNumber_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "Procurement_procurementNumber_idx" ON public."Procurement" USING btree ("procurementNumber");


--
-- Name: Procurement_procurementNumber_key; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE UNIQUE INDEX "Procurement_procurementNumber_key" ON public."Procurement" USING btree ("procurementNumber");


--
-- Name: Procurement_status_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "Procurement_status_idx" ON public."Procurement" USING btree (status);


--
-- Name: PurchaseOrder_poNumber_key; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE UNIQUE INDEX "PurchaseOrder_poNumber_key" ON public."PurchaseOrder" USING btree ("poNumber");


--
-- Name: PurchaseOrder_procurementId_key; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE UNIQUE INDEX "PurchaseOrder_procurementId_key" ON public."PurchaseOrder" USING btree ("procurementId");


--
-- Name: PurchaseOrder_vendorId_status_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "PurchaseOrder_vendorId_status_idx" ON public."PurchaseOrder" USING btree ("vendorId", status);


--
-- Name: PurchaseRequisition_prNumber_key; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE UNIQUE INDEX "PurchaseRequisition_prNumber_key" ON public."PurchaseRequisition" USING btree ("prNumber");


--
-- Name: PurchaseRequisition_procurementId_key; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE UNIQUE INDEX "PurchaseRequisition_procurementId_key" ON public."PurchaseRequisition" USING btree ("procurementId");


--
-- Name: ResourcePermission_resource_action_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "ResourcePermission_resource_action_idx" ON public."ResourcePermission" USING btree (resource, action);


--
-- Name: ResourcePermission_roleId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "ResourcePermission_roleId_idx" ON public."ResourcePermission" USING btree ("roleId");


--
-- Name: ResourcePermission_userId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "ResourcePermission_userId_idx" ON public."ResourcePermission" USING btree ("userId");


--
-- Name: Role_name_key; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE UNIQUE INDEX "Role_name_key" ON public."Role" USING btree (name);


--
-- Name: SanctionedIndividual_identityNumber_key; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE UNIQUE INDEX "SanctionedIndividual_identityNumber_key" ON public."SanctionedIndividual" USING btree ("identityNumber");


--
-- Name: TaxExemption_entityType_entityId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "TaxExemption_entityType_entityId_idx" ON public."TaxExemption" USING btree ("entityType", "entityId");


--
-- Name: TaxExemption_isActive_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "TaxExemption_isActive_idx" ON public."TaxExemption" USING btree ("isActive");


--
-- Name: TaxExemption_taxTypeId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "TaxExemption_taxTypeId_idx" ON public."TaxExemption" USING btree ("taxTypeId");


--
-- Name: TaxType_name_key; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE UNIQUE INDEX "TaxType_name_key" ON public."TaxType" USING btree (name);


--
-- Name: UserRole_userId_roleId_key; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE UNIQUE INDEX "UserRole_userId_roleId_key" ON public."UserRole" USING btree ("userId", "roleId");


--
-- Name: User_email_key; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE UNIQUE INDEX "User_email_key" ON public."User" USING btree (email);


--
-- Name: VendorEvaluation_procurementId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "VendorEvaluation_procurementId_idx" ON public."VendorEvaluation" USING btree ("procurementId");


--
-- Name: VendorEvaluation_procurementId_vendorId_key; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE UNIQUE INDEX "VendorEvaluation_procurementId_vendorId_key" ON public."VendorEvaluation" USING btree ("procurementId", "vendorId");


--
-- Name: VendorEvaluation_vendorId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "VendorEvaluation_vendorId_idx" ON public."VendorEvaluation" USING btree ("vendorId");


--
-- Name: VendorIssue_assignedToId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "VendorIssue_assignedToId_idx" ON public."VendorIssue" USING btree ("assignedToId");


--
-- Name: VendorIssue_reportedById_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "VendorIssue_reportedById_idx" ON public."VendorIssue" USING btree ("reportedById");


--
-- Name: VendorIssue_severity_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "VendorIssue_severity_idx" ON public."VendorIssue" USING btree (severity);


--
-- Name: VendorIssue_status_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "VendorIssue_status_idx" ON public."VendorIssue" USING btree (status);


--
-- Name: VendorIssue_vendorId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "VendorIssue_vendorId_idx" ON public."VendorIssue" USING btree ("vendorId");


--
-- Name: VendorKpiEvaluation_evaluationPeriod_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "VendorKpiEvaluation_evaluationPeriod_idx" ON public."VendorKpiEvaluation" USING btree ("evaluationPeriod");


--
-- Name: VendorKpiEvaluation_rating_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "VendorKpiEvaluation_rating_idx" ON public."VendorKpiEvaluation" USING btree (rating);


--
-- Name: VendorKpiEvaluation_templateId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "VendorKpiEvaluation_templateId_idx" ON public."VendorKpiEvaluation" USING btree ("templateId");


--
-- Name: VendorKpiEvaluation_vendorId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "VendorKpiEvaluation_vendorId_idx" ON public."VendorKpiEvaluation" USING btree ("vendorId");


--
-- Name: VendorKpiEvaluation_vendorId_templateId_evaluationPeriod_key; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE UNIQUE INDEX "VendorKpiEvaluation_vendorId_templateId_evaluationPeriod_key" ON public."VendorKpiEvaluation" USING btree ("vendorId", "templateId", "evaluationPeriod");


--
-- Name: VendorKpiMetric_category_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "VendorKpiMetric_category_idx" ON public."VendorKpiMetric" USING btree (category);


--
-- Name: VendorKpiMetric_evaluationPeriod_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "VendorKpiMetric_evaluationPeriod_idx" ON public."VendorKpiMetric" USING btree ("evaluationPeriod");


--
-- Name: VendorKpiMetric_metricName_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "VendorKpiMetric_metricName_idx" ON public."VendorKpiMetric" USING btree ("metricName");


--
-- Name: VendorKpiMetric_vendorId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "VendorKpiMetric_vendorId_idx" ON public."VendorKpiMetric" USING btree ("vendorId");


--
-- Name: VendorKpiScore_evaluationId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "VendorKpiScore_evaluationId_idx" ON public."VendorKpiScore" USING btree ("evaluationId");


--
-- Name: VendorKpiScore_evaluationId_metricId_key; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE UNIQUE INDEX "VendorKpiScore_evaluationId_metricId_key" ON public."VendorKpiScore" USING btree ("evaluationId", "metricId");


--
-- Name: VendorKpiScore_metricId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "VendorKpiScore_metricId_idx" ON public."VendorKpiScore" USING btree ("metricId");


--
-- Name: VendorKpiTemplate_category_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "VendorKpiTemplate_category_idx" ON public."VendorKpiTemplate" USING btree (category);


--
-- Name: VendorKpiTemplate_isActive_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "VendorKpiTemplate_isActive_idx" ON public."VendorKpiTemplate" USING btree ("isActive");


--
-- Name: VendorKpi_vendorId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "VendorKpi_vendorId_idx" ON public."VendorKpi" USING btree ("vendorId");


--
-- Name: VendorOffer_procurementId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "VendorOffer_procurementId_idx" ON public."VendorOffer" USING btree ("procurementId");


--
-- Name: VendorOffer_procurementId_vendorId_key; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE UNIQUE INDEX "VendorOffer_procurementId_vendorId_key" ON public."VendorOffer" USING btree ("procurementId", "vendorId");


--
-- Name: VendorOffer_status_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "VendorOffer_status_idx" ON public."VendorOffer" USING btree (status);


--
-- Name: VendorOffer_vendorId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "VendorOffer_vendorId_idx" ON public."VendorOffer" USING btree ("vendorId");


--
-- Name: VendorPerformanceHistory_overallScore_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "VendorPerformanceHistory_overallScore_idx" ON public."VendorPerformanceHistory" USING btree ("overallScore");


--
-- Name: VendorPerformanceHistory_period_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "VendorPerformanceHistory_period_idx" ON public."VendorPerformanceHistory" USING btree (period);


--
-- Name: VendorPerformanceHistory_vendorId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "VendorPerformanceHistory_vendorId_idx" ON public."VendorPerformanceHistory" USING btree ("vendorId");


--
-- Name: VendorPerformanceHistory_vendorId_period_key; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE UNIQUE INDEX "VendorPerformanceHistory_vendorId_period_key" ON public."VendorPerformanceHistory" USING btree ("vendorId", period);


--
-- Name: VendorRequirementTemplate_category_type_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "VendorRequirementTemplate_category_type_idx" ON public."VendorRequirementTemplate" USING btree (category, type);


--
-- Name: VendorRequirementTemplate_templateId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "VendorRequirementTemplate_templateId_idx" ON public."VendorRequirementTemplate" USING btree ("templateId");


--
-- Name: VendorVerificationStep_vendorId_stepName_key; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE UNIQUE INDEX "VendorVerificationStep_vendorId_stepName_key" ON public."VendorVerificationStep" USING btree ("vendorId", "stepName");


--
-- Name: Vendor_businessLicense_key; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE UNIQUE INDEX "Vendor_businessLicense_key" ON public."Vendor" USING btree ("businessLicense");


--
-- Name: Vendor_taxId_key; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE UNIQUE INDEX "Vendor_taxId_key" ON public."Vendor" USING btree ("taxId");


--
-- Name: Vendor_userId_key; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE UNIQUE INDEX "Vendor_userId_key" ON public."Vendor" USING btree ("userId");


--
-- Name: audit_logs_action_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX audit_logs_action_idx ON public.audit_logs USING btree (action);


--
-- Name: audit_logs_archive_action_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX audit_logs_archive_action_idx ON public.audit_logs_archive USING btree (action);


--
-- Name: audit_logs_archive_archivedAt_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "audit_logs_archive_archivedAt_idx" ON public.audit_logs_archive USING btree ("archivedAt");


--
-- Name: audit_logs_archive_originalId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "audit_logs_archive_originalId_idx" ON public.audit_logs_archive USING btree ("originalId");


--
-- Name: audit_logs_archive_originalId_key; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE UNIQUE INDEX "audit_logs_archive_originalId_key" ON public.audit_logs_archive USING btree ("originalId");


--
-- Name: audit_logs_archive_timestamp_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX audit_logs_archive_timestamp_idx ON public.audit_logs_archive USING btree ("timestamp");


--
-- Name: audit_logs_category_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX audit_logs_category_idx ON public.audit_logs USING btree (category);


--
-- Name: audit_logs_entityType_entityId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "audit_logs_entityType_entityId_idx" ON public.audit_logs USING btree ("entityType", "entityId");


--
-- Name: audit_logs_partitionDate_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "audit_logs_partitionDate_idx" ON public.audit_logs USING btree ("partitionDate");


--
-- Name: audit_logs_procurementId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "audit_logs_procurementId_idx" ON public.audit_logs USING btree ("procurementId");


--
-- Name: audit_logs_resourceId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "audit_logs_resourceId_idx" ON public.audit_logs USING btree ("resourceId");


--
-- Name: audit_logs_resource_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX audit_logs_resource_idx ON public.audit_logs USING btree (resource);


--
-- Name: audit_logs_severity_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX audit_logs_severity_idx ON public.audit_logs USING btree (severity);


--
-- Name: audit_logs_timestamp_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX audit_logs_timestamp_idx ON public.audit_logs USING btree ("timestamp");


--
-- Name: audit_logs_userId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "audit_logs_userId_idx" ON public.audit_logs USING btree ("userId");


--
-- Name: db_performance_logs_partitionDate_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "db_performance_logs_partitionDate_idx" ON public.db_performance_logs USING btree ("partitionDate");


--
-- Name: db_performance_logs_queryHash_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "db_performance_logs_queryHash_idx" ON public.db_performance_logs USING btree ("queryHash");


--
-- Name: db_performance_logs_tableName_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "db_performance_logs_tableName_idx" ON public.db_performance_logs USING btree ("tableName");


--
-- Name: db_performance_logs_timestamp_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX db_performance_logs_timestamp_idx ON public.db_performance_logs USING btree ("timestamp");


--
-- Name: document_template_versions_templateId_version_key; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE UNIQUE INDEX "document_template_versions_templateId_version_key" ON public.document_template_versions USING btree ("templateId", version);


--
-- Name: email_queue_createdAt_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "email_queue_createdAt_idx" ON public.email_queue USING btree ("createdAt");


--
-- Name: email_queue_priority_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX email_queue_priority_idx ON public.email_queue USING btree (priority);


--
-- Name: email_queue_retryCount_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "email_queue_retryCount_idx" ON public.email_queue USING btree ("retryCount");


--
-- Name: email_queue_scheduledAt_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "email_queue_scheduledAt_idx" ON public.email_queue USING btree ("scheduledAt");


--
-- Name: email_queue_sentAt_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "email_queue_sentAt_idx" ON public.email_queue USING btree ("sentAt");


--
-- Name: email_queue_status_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX email_queue_status_idx ON public.email_queue USING btree (status);


--
-- Name: public_assets_category_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX public_assets_category_idx ON public.public_assets USING btree (category);


--
-- Name: public_assets_expiresAt_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "public_assets_expiresAt_idx" ON public.public_assets USING btree ("expiresAt");


--
-- Name: public_assets_filePath_key; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE UNIQUE INDEX "public_assets_filePath_key" ON public.public_assets USING btree ("filePath");


--
-- Name: public_assets_fileType_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "public_assets_fileType_idx" ON public.public_assets USING btree ("fileType");


--
-- Name: public_assets_isActive_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "public_assets_isActive_idx" ON public.public_assets USING btree ("isActive");


--
-- Name: public_assets_isPublic_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "public_assets_isPublic_idx" ON public.public_assets USING btree ("isPublic");


--
-- Name: public_assets_publishedAt_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "public_assets_publishedAt_idx" ON public.public_assets USING btree ("publishedAt");


--
-- Name: public_assets_securityLevel_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "public_assets_securityLevel_idx" ON public.public_assets USING btree ("securityLevel");


--
-- Name: public_assets_size_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX public_assets_size_idx ON public.public_assets USING btree (size);


--
-- Name: public_assets_tags_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX public_assets_tags_idx ON public.public_assets USING btree (tags);


--
-- Name: public_assets_uploadedById_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "public_assets_uploadedById_idx" ON public.public_assets USING btree ("uploadedById");


--
-- Name: push_queue_createdAt_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "push_queue_createdAt_idx" ON public.push_queue USING btree ("createdAt");


--
-- Name: push_queue_priority_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX push_queue_priority_idx ON public.push_queue USING btree (priority);


--
-- Name: push_queue_scheduledAt_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "push_queue_scheduledAt_idx" ON public.push_queue USING btree ("scheduledAt");


--
-- Name: push_queue_status_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX push_queue_status_idx ON public.push_queue USING btree (status);


--
-- Name: push_queue_userId_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "push_queue_userId_idx" ON public.push_queue USING btree ("userId");


--
-- Name: sms_queue_createdAt_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "sms_queue_createdAt_idx" ON public.sms_queue USING btree ("createdAt");


--
-- Name: sms_queue_priority_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX sms_queue_priority_idx ON public.sms_queue USING btree (priority);


--
-- Name: sms_queue_scheduledAt_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "sms_queue_scheduledAt_idx" ON public.sms_queue USING btree ("scheduledAt");


--
-- Name: sms_queue_status_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX sms_queue_status_idx ON public.sms_queue USING btree (status);


--
-- Name: webhook_queue_createdAt_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "webhook_queue_createdAt_idx" ON public.webhook_queue USING btree ("createdAt");


--
-- Name: webhook_queue_priority_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX webhook_queue_priority_idx ON public.webhook_queue USING btree (priority);


--
-- Name: webhook_queue_scheduledAt_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX "webhook_queue_scheduledAt_idx" ON public.webhook_queue USING btree ("scheduledAt");


--
-- Name: webhook_queue_status_idx; Type: INDEX; Schema: public; Owner: avnadmin
--

CREATE INDEX webhook_queue_status_idx ON public.webhook_queue USING btree (status);


--
-- Name: ApprovalAction ApprovalAction_delegatedToId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ApprovalAction"
    ADD CONSTRAINT "ApprovalAction_delegatedToId_fkey" FOREIGN KEY ("delegatedToId") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: ApprovalAction ApprovalAction_performedById_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ApprovalAction"
    ADD CONSTRAINT "ApprovalAction_performedById_fkey" FOREIGN KEY ("performedById") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: ApprovalAction ApprovalAction_stepInstanceId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ApprovalAction"
    ADD CONSTRAINT "ApprovalAction_stepInstanceId_fkey" FOREIGN KEY ("stepInstanceId") REFERENCES public."ApprovalStepInstance"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: ApprovalComment ApprovalComment_authorId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ApprovalComment"
    ADD CONSTRAINT "ApprovalComment_authorId_fkey" FOREIGN KEY ("authorId") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: ApprovalComment ApprovalComment_instanceId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ApprovalComment"
    ADD CONSTRAINT "ApprovalComment_instanceId_fkey" FOREIGN KEY ("instanceId") REFERENCES public."ApprovalInstance"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: ApprovalDelegation ApprovalDelegation_fromUserId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ApprovalDelegation"
    ADD CONSTRAINT "ApprovalDelegation_fromUserId_fkey" FOREIGN KEY ("fromUserId") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: ApprovalDelegation ApprovalDelegation_toUserId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ApprovalDelegation"
    ADD CONSTRAINT "ApprovalDelegation_toUserId_fkey" FOREIGN KEY ("toUserId") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: ApprovalInstance ApprovalInstance_initiatedById_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ApprovalInstance"
    ADD CONSTRAINT "ApprovalInstance_initiatedById_fkey" FOREIGN KEY ("initiatedById") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: ApprovalInstance ApprovalInstance_purchaseRequisitionId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ApprovalInstance"
    ADD CONSTRAINT "ApprovalInstance_purchaseRequisitionId_fkey" FOREIGN KEY ("purchaseRequisitionId") REFERENCES public."PurchaseRequisition"(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: ApprovalInstance ApprovalInstance_workflowId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ApprovalInstance"
    ADD CONSTRAINT "ApprovalInstance_workflowId_fkey" FOREIGN KEY ("workflowId") REFERENCES public."ApprovalWorkflow"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: ApprovalStepInstance ApprovalStepInstance_instanceId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ApprovalStepInstance"
    ADD CONSTRAINT "ApprovalStepInstance_instanceId_fkey" FOREIGN KEY ("instanceId") REFERENCES public."ApprovalInstance"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: ApprovalStepInstance ApprovalStepInstance_stepId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ApprovalStepInstance"
    ADD CONSTRAINT "ApprovalStepInstance_stepId_fkey" FOREIGN KEY ("stepId") REFERENCES public."ApprovalStep"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: ApprovalStep ApprovalStep_workflowId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ApprovalStep"
    ADD CONSTRAINT "ApprovalStep_workflowId_fkey" FOREIGN KEY ("workflowId") REFERENCES public."ApprovalWorkflow"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: ApprovalWorkflow ApprovalWorkflow_createdById_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ApprovalWorkflow"
    ADD CONSTRAINT "ApprovalWorkflow_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: Approval Approval_approverId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."Approval"
    ADD CONSTRAINT "Approval_approverId_fkey" FOREIGN KEY ("approverId") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: Approval Approval_bastId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."Approval"
    ADD CONSTRAINT "Approval_bastId_fkey" FOREIGN KEY ("bastId") REFERENCES public."BAST"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: Approval Approval_invoiceId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."Approval"
    ADD CONSTRAINT "Approval_invoiceId_fkey" FOREIGN KEY ("invoiceId") REFERENCES public."Invoice"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: Approval Approval_poId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."Approval"
    ADD CONSTRAINT "Approval_poId_fkey" FOREIGN KEY ("poId") REFERENCES public."PurchaseOrder"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: BAST BAST_createdById_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."BAST"
    ADD CONSTRAINT "BAST_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: BAST BAST_poId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."BAST"
    ADD CONSTRAINT "BAST_poId_fkey" FOREIGN KEY ("poId") REFERENCES public."PurchaseOrder"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: BastChecklistItem BastChecklistItem_bastId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."BastChecklistItem"
    ADD CONSTRAINT "BastChecklistItem_bastId_fkey" FOREIGN KEY ("bastId") REFERENCES public."BAST"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: BlacklistEntry BlacklistEntry_appealDecidedById_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."BlacklistEntry"
    ADD CONSTRAINT "BlacklistEntry_appealDecidedById_fkey" FOREIGN KEY ("appealDecidedById") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: BlacklistEntry BlacklistEntry_createdById_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."BlacklistEntry"
    ADD CONSTRAINT "BlacklistEntry_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: BlacklistEntry BlacklistEntry_vendorId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."BlacklistEntry"
    ADD CONSTRAINT "BlacklistEntry_vendorId_fkey" FOREIGN KEY ("vendorId") REFERENCES public."Vendor"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: ContentApproval ContentApproval_contentId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ContentApproval"
    ADD CONSTRAINT "ContentApproval_contentId_fkey" FOREIGN KEY ("contentId") REFERENCES public."NewsArticle"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: ContentApproval ContentApproval_requestedById_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ContentApproval"
    ADD CONSTRAINT "ContentApproval_requestedById_fkey" FOREIGN KEY ("requestedById") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: ContentApproval ContentApproval_reviewedById_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ContentApproval"
    ADD CONSTRAINT "ContentApproval_reviewedById_fkey" FOREIGN KEY ("reviewedById") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: Content Content_authorId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."Content"
    ADD CONSTRAINT "Content_authorId_fkey" FOREIGN KEY ("authorId") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: Contract Contract_vendorId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."Contract"
    ADD CONSTRAINT "Contract_vendorId_fkey" FOREIGN KEY ("vendorId") REFERENCES public."Vendor"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: DeliveryItem DeliveryItem_deliveryId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."DeliveryItem"
    ADD CONSTRAINT "DeliveryItem_deliveryId_fkey" FOREIGN KEY ("deliveryId") REFERENCES public."Delivery"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: DeliveryItem DeliveryItem_poItemId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."DeliveryItem"
    ADD CONSTRAINT "DeliveryItem_poItemId_fkey" FOREIGN KEY ("poItemId") REFERENCES public."PurchaseOrderItem"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: Delivery Delivery_createdById_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."Delivery"
    ADD CONSTRAINT "Delivery_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: Delivery Delivery_inspectedById_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."Delivery"
    ADD CONSTRAINT "Delivery_inspectedById_fkey" FOREIGN KEY ("inspectedById") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: Delivery Delivery_poId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."Delivery"
    ADD CONSTRAINT "Delivery_poId_fkey" FOREIGN KEY ("poId") REFERENCES public."PurchaseOrder"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: DiscussionAttachment DiscussionAttachment_discussionId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."DiscussionAttachment"
    ADD CONSTRAINT "DiscussionAttachment_discussionId_fkey" FOREIGN KEY ("discussionId") REFERENCES public."ProcurementDiscussion"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: DiscussionAttachment DiscussionAttachment_uploadedById_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."DiscussionAttachment"
    ADD CONSTRAINT "DiscussionAttachment_uploadedById_fkey" FOREIGN KEY ("uploadedById") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: DiscussionMessage DiscussionMessage_authorId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."DiscussionMessage"
    ADD CONSTRAINT "DiscussionMessage_authorId_fkey" FOREIGN KEY ("authorId") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: DiscussionMessage DiscussionMessage_discussionId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."DiscussionMessage"
    ADD CONSTRAINT "DiscussionMessage_discussionId_fkey" FOREIGN KEY ("discussionId") REFERENCES public."ProcurementDiscussion"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: DiscussionMessage DiscussionMessage_parentId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."DiscussionMessage"
    ADD CONSTRAINT "DiscussionMessage_parentId_fkey" FOREIGN KEY ("parentId") REFERENCES public."DiscussionMessage"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: DiscussionParticipant DiscussionParticipant_discussionId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."DiscussionParticipant"
    ADD CONSTRAINT "DiscussionParticipant_discussionId_fkey" FOREIGN KEY ("discussionId") REFERENCES public."ProcurementDiscussion"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: DiscussionParticipant DiscussionParticipant_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."DiscussionParticipant"
    ADD CONSTRAINT "DiscussionParticipant_userId_fkey" FOREIGN KEY ("userId") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: DiscussionThread DiscussionThread_procurementStageId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."DiscussionThread"
    ADD CONSTRAINT "DiscussionThread_procurementStageId_fkey" FOREIGN KEY ("procurementStageId") REFERENCES public."ProcurementStage"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: Document Document_bastId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."Document"
    ADD CONSTRAINT "Document_bastId_fkey" FOREIGN KEY ("bastId") REFERENCES public."BAST"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: Document Document_invoiceId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."Document"
    ADD CONSTRAINT "Document_invoiceId_fkey" FOREIGN KEY ("invoiceId") REFERENCES public."Invoice"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: Document Document_poId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."Document"
    ADD CONSTRAINT "Document_poId_fkey" FOREIGN KEY ("poId") REFERENCES public."PurchaseOrder"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: Document Document_prId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."Document"
    ADD CONSTRAINT "Document_prId_fkey" FOREIGN KEY ("prId") REFERENCES public."PurchaseRequisition"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: Document Document_procurementId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."Document"
    ADD CONSTRAINT "Document_procurementId_fkey" FOREIGN KEY ("procurementId") REFERENCES public."Procurement"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: Document Document_vendorId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."Document"
    ADD CONSTRAINT "Document_vendorId_fkey" FOREIGN KEY ("vendorId") REFERENCES public."Vendor"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: Document Document_vendorOfferId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."Document"
    ADD CONSTRAINT "Document_vendorOfferId_fkey" FOREIGN KEY ("vendorOfferId") REFERENCES public."VendorOffer"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: GoodReceiptItem GoodReceiptItem_grId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."GoodReceiptItem"
    ADD CONSTRAINT "GoodReceiptItem_grId_fkey" FOREIGN KEY ("grId") REFERENCES public."GoodReceipt"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: GoodReceiptItem GoodReceiptItem_grnId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."GoodReceiptItem"
    ADD CONSTRAINT "GoodReceiptItem_grnId_fkey" FOREIGN KEY ("grnId") REFERENCES public."GoodsReceiptNote"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: GoodReceiptItem GoodReceiptItem_purchaseOrderItemId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."GoodReceiptItem"
    ADD CONSTRAINT "GoodReceiptItem_purchaseOrderItemId_fkey" FOREIGN KEY ("purchaseOrderItemId") REFERENCES public."PurchaseOrderItem"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: GoodReceipt GoodReceipt_createdById_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."GoodReceipt"
    ADD CONSTRAINT "GoodReceipt_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: GoodReceipt GoodReceipt_poId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."GoodReceipt"
    ADD CONSTRAINT "GoodReceipt_poId_fkey" FOREIGN KEY ("poId") REFERENCES public."PurchaseOrder"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: GoodsReceiptNote GoodsReceiptNote_approvedById_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."GoodsReceiptNote"
    ADD CONSTRAINT "GoodsReceiptNote_approvedById_fkey" FOREIGN KEY ("approvedById") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: GoodsReceiptNote GoodsReceiptNote_createdById_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."GoodsReceiptNote"
    ADD CONSTRAINT "GoodsReceiptNote_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: GoodsReceiptNote GoodsReceiptNote_poId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."GoodsReceiptNote"
    ADD CONSTRAINT "GoodsReceiptNote_poId_fkey" FOREIGN KEY ("poId") REFERENCES public."PurchaseOrder"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: Invoice Invoice_approvedById_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."Invoice"
    ADD CONSTRAINT "Invoice_approvedById_fkey" FOREIGN KEY ("approvedById") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: Invoice Invoice_poId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."Invoice"
    ADD CONSTRAINT "Invoice_poId_fkey" FOREIGN KEY ("poId") REFERENCES public."PurchaseOrder"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: Invoice Invoice_submittedById_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."Invoice"
    ADD CONSTRAINT "Invoice_submittedById_fkey" FOREIGN KEY ("submittedById") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: MessageAttachment MessageAttachment_messageId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."MessageAttachment"
    ADD CONSTRAINT "MessageAttachment_messageId_fkey" FOREIGN KEY ("messageId") REFERENCES public."DiscussionMessage"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: MessageAttachment MessageAttachment_uploadedById_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."MessageAttachment"
    ADD CONSTRAINT "MessageAttachment_uploadedById_fkey" FOREIGN KEY ("uploadedById") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: MessageReaction MessageReaction_messageId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."MessageReaction"
    ADD CONSTRAINT "MessageReaction_messageId_fkey" FOREIGN KEY ("messageId") REFERENCES public."DiscussionMessage"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: MessageReaction MessageReaction_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."MessageReaction"
    ADD CONSTRAINT "MessageReaction_userId_fkey" FOREIGN KEY ("userId") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: NegotiationRound NegotiationRound_proposedById_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."NegotiationRound"
    ADD CONSTRAINT "NegotiationRound_proposedById_fkey" FOREIGN KEY ("proposedById") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: NegotiationRound NegotiationRound_respondedById_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."NegotiationRound"
    ADD CONSTRAINT "NegotiationRound_respondedById_fkey" FOREIGN KEY ("respondedById") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: NegotiationRound NegotiationRound_sessionId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."NegotiationRound"
    ADD CONSTRAINT "NegotiationRound_sessionId_fkey" FOREIGN KEY ("sessionId") REFERENCES public."NegotiationSession"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: NegotiationSession NegotiationSession_initiatedById_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."NegotiationSession"
    ADD CONSTRAINT "NegotiationSession_initiatedById_fkey" FOREIGN KEY ("initiatedById") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: NegotiationSession NegotiationSession_procurementId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."NegotiationSession"
    ADD CONSTRAINT "NegotiationSession_procurementId_fkey" FOREIGN KEY ("procurementId") REFERENCES public."Procurement"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: NegotiationSession NegotiationSession_vendorId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."NegotiationSession"
    ADD CONSTRAINT "NegotiationSession_vendorId_fkey" FOREIGN KEY ("vendorId") REFERENCES public."Vendor"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: NewsArticle NewsArticle_authorId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."NewsArticle"
    ADD CONSTRAINT "NewsArticle_authorId_fkey" FOREIGN KEY ("authorId") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: NewsArticle NewsArticle_categoryId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."NewsArticle"
    ADD CONSTRAINT "NewsArticle_categoryId_fkey" FOREIGN KEY ("categoryId") REFERENCES public."NewsCategory"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: NotificationEscalation NotificationEscalation_templateId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."NotificationEscalation"
    ADD CONSTRAINT "NotificationEscalation_templateId_fkey" FOREIGN KEY ("templateId") REFERENCES public."NotificationTemplate"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: NotificationTemplate NotificationTemplate_createdById_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."NotificationTemplate"
    ADD CONSTRAINT "NotificationTemplate_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: NotificationTemplate NotificationTemplate_parentId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."NotificationTemplate"
    ADD CONSTRAINT "NotificationTemplate_parentId_fkey" FOREIGN KEY ("parentId") REFERENCES public."NotificationTemplate"(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: OfferEvaluation OfferEvaluation_evaluatedById_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."OfferEvaluation"
    ADD CONSTRAINT "OfferEvaluation_evaluatedById_fkey" FOREIGN KEY ("evaluatedById") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: OfferEvaluation OfferEvaluation_offerId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."OfferEvaluation"
    ADD CONSTRAINT "OfferEvaluation_offerId_fkey" FOREIGN KEY ("offerId") REFERENCES public."VendorOffer"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: Offer Offer_procurementId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."Offer"
    ADD CONSTRAINT "Offer_procurementId_fkey" FOREIGN KEY ("procurementId") REFERENCES public."Procurement"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: Offer Offer_vendorId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."Offer"
    ADD CONSTRAINT "Offer_vendorId_fkey" FOREIGN KEY ("vendorId") REFERENCES public."Vendor"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: PriceCorrectionLog PriceCorrectionLog_correctedById_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."PriceCorrectionLog"
    ADD CONSTRAINT "PriceCorrectionLog_correctedById_fkey" FOREIGN KEY ("correctedById") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: PriceCorrectionLog PriceCorrectionLog_offerItemId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."PriceCorrectionLog"
    ADD CONSTRAINT "PriceCorrectionLog_offerItemId_fkey" FOREIGN KEY ("offerItemId") REFERENCES public."VendorOfferItem"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: ProcurementCommitteeMember ProcurementCommitteeMember_procurementId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ProcurementCommitteeMember"
    ADD CONSTRAINT "ProcurementCommitteeMember_procurementId_fkey" FOREIGN KEY ("procurementId") REFERENCES public."Procurement"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: ProcurementCommitteeMember ProcurementCommitteeMember_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ProcurementCommitteeMember"
    ADD CONSTRAINT "ProcurementCommitteeMember_userId_fkey" FOREIGN KEY ("userId") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: ProcurementContent ProcurementContent_authorId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ProcurementContent"
    ADD CONSTRAINT "ProcurementContent_authorId_fkey" FOREIGN KEY ("authorId") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: ProcurementDiscussion ProcurementDiscussion_createdById_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ProcurementDiscussion"
    ADD CONSTRAINT "ProcurementDiscussion_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: ProcurementDiscussion ProcurementDiscussion_procurementId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ProcurementDiscussion"
    ADD CONSTRAINT "ProcurementDiscussion_procurementId_fkey" FOREIGN KEY ("procurementId") REFERENCES public."Procurement"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: ProcurementItemInclude ProcurementItemInclude_itemId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ProcurementItemInclude"
    ADD CONSTRAINT "ProcurementItemInclude_itemId_fkey" FOREIGN KEY ("itemId") REFERENCES public."ProcurementItem"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: ProcurementItem ProcurementItem_procurementId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ProcurementItem"
    ADD CONSTRAINT "ProcurementItem_procurementId_fkey" FOREIGN KEY ("procurementId") REFERENCES public."Procurement"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: ProcurementMilestone ProcurementMilestone_scheduleId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ProcurementMilestone"
    ADD CONSTRAINT "ProcurementMilestone_scheduleId_fkey" FOREIGN KEY ("scheduleId") REFERENCES public."ProcurementSchedule"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: ProcurementPackage ProcurementPackage_createdById_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ProcurementPackage"
    ADD CONSTRAINT "ProcurementPackage_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: ProcurementPackage ProcurementPackage_procurementId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ProcurementPackage"
    ADD CONSTRAINT "ProcurementPackage_procurementId_fkey" FOREIGN KEY ("procurementId") REFERENCES public."Procurement"(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: ProcurementScheduleTemplate ProcurementScheduleTemplate_createdBy_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ProcurementScheduleTemplate"
    ADD CONSTRAINT "ProcurementScheduleTemplate_createdBy_fkey" FOREIGN KEY ("createdBy") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: ProcurementScheduleTemplate ProcurementScheduleTemplate_templateId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ProcurementScheduleTemplate"
    ADD CONSTRAINT "ProcurementScheduleTemplate_templateId_fkey" FOREIGN KEY ("templateId") REFERENCES public."ProcurementWorkflowTemplate"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: ProcurementSchedule ProcurementSchedule_createdBy_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ProcurementSchedule"
    ADD CONSTRAINT "ProcurementSchedule_createdBy_fkey" FOREIGN KEY ("createdBy") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: ProcurementSchedule ProcurementSchedule_procurementId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ProcurementSchedule"
    ADD CONSTRAINT "ProcurementSchedule_procurementId_fkey" FOREIGN KEY ("procurementId") REFERENCES public."Procurement"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: ProcurementSchedule ProcurementSchedule_templateId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ProcurementSchedule"
    ADD CONSTRAINT "ProcurementSchedule_templateId_fkey" FOREIGN KEY ("templateId") REFERENCES public."ProcurementScheduleTemplate"(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: ProcurementStage ProcurementStage_procurementId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ProcurementStage"
    ADD CONSTRAINT "ProcurementStage_procurementId_fkey" FOREIGN KEY ("procurementId") REFERENCES public."Procurement"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: ProcurementVendorRequirement ProcurementVendorRequirement_procurementId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ProcurementVendorRequirement"
    ADD CONSTRAINT "ProcurementVendorRequirement_procurementId_fkey" FOREIGN KEY ("procurementId") REFERENCES public."Procurement"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: ProcurementVendorRequirement ProcurementVendorRequirement_requirementId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ProcurementVendorRequirement"
    ADD CONSTRAINT "ProcurementVendorRequirement_requirementId_fkey" FOREIGN KEY ("requirementId") REFERENCES public."VendorRequirementTemplate"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: ProcurementWorkflowStageTemplate ProcurementWorkflowStageTemplate_templateId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ProcurementWorkflowStageTemplate"
    ADD CONSTRAINT "ProcurementWorkflowStageTemplate_templateId_fkey" FOREIGN KEY ("templateId") REFERENCES public."ProcurementWorkflowTemplate"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: ProcurementWorkflowTemplate ProcurementWorkflowTemplate_createdBy_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ProcurementWorkflowTemplate"
    ADD CONSTRAINT "ProcurementWorkflowTemplate_createdBy_fkey" FOREIGN KEY ("createdBy") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: Procurement Procurement_createdById_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."Procurement"
    ADD CONSTRAINT "Procurement_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: Procurement Procurement_evaluationTemplateId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."Procurement"
    ADD CONSTRAINT "Procurement_evaluationTemplateId_fkey" FOREIGN KEY ("evaluationTemplateId") REFERENCES public."EvaluationTemplate"(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: Procurement Procurement_workflowTemplateId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."Procurement"
    ADD CONSTRAINT "Procurement_workflowTemplateId_fkey" FOREIGN KEY ("workflowTemplateId") REFERENCES public."ProcurementWorkflowTemplate"(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: PurchaseOrderItem PurchaseOrderItem_itemId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."PurchaseOrderItem"
    ADD CONSTRAINT "PurchaseOrderItem_itemId_fkey" FOREIGN KEY ("itemId") REFERENCES public."ProcurementItem"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: PurchaseOrderItem PurchaseOrderItem_poId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."PurchaseOrderItem"
    ADD CONSTRAINT "PurchaseOrderItem_poId_fkey" FOREIGN KEY ("poId") REFERENCES public."PurchaseOrder"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: PurchaseOrder PurchaseOrder_approvedById_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."PurchaseOrder"
    ADD CONSTRAINT "PurchaseOrder_approvedById_fkey" FOREIGN KEY ("approvedById") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: PurchaseOrder PurchaseOrder_createdById_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."PurchaseOrder"
    ADD CONSTRAINT "PurchaseOrder_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: PurchaseOrder PurchaseOrder_procurementId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."PurchaseOrder"
    ADD CONSTRAINT "PurchaseOrder_procurementId_fkey" FOREIGN KEY ("procurementId") REFERENCES public."Procurement"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: PurchaseOrder PurchaseOrder_vendorId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."PurchaseOrder"
    ADD CONSTRAINT "PurchaseOrder_vendorId_fkey" FOREIGN KEY ("vendorId") REFERENCES public."Vendor"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: PurchaseRequisitionItem PurchaseRequisitionItem_itemMasterId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."PurchaseRequisitionItem"
    ADD CONSTRAINT "PurchaseRequisitionItem_itemMasterId_fkey" FOREIGN KEY ("itemMasterId") REFERENCES public."ItemMaster"(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: PurchaseRequisitionItem PurchaseRequisitionItem_prId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."PurchaseRequisitionItem"
    ADD CONSTRAINT "PurchaseRequisitionItem_prId_fkey" FOREIGN KEY ("prId") REFERENCES public."PurchaseRequisition"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: PurchaseRequisition PurchaseRequisition_packageId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."PurchaseRequisition"
    ADD CONSTRAINT "PurchaseRequisition_packageId_fkey" FOREIGN KEY ("packageId") REFERENCES public."ProcurementPackage"(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: PurchaseRequisition PurchaseRequisition_procurementId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."PurchaseRequisition"
    ADD CONSTRAINT "PurchaseRequisition_procurementId_fkey" FOREIGN KEY ("procurementId") REFERENCES public."Procurement"(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: PurchaseRequisition PurchaseRequisition_requesterId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."PurchaseRequisition"
    ADD CONSTRAINT "PurchaseRequisition_requesterId_fkey" FOREIGN KEY ("requesterId") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: PurchaseRequisition PurchaseRequisition_sourceContractId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."PurchaseRequisition"
    ADD CONSTRAINT "PurchaseRequisition_sourceContractId_fkey" FOREIGN KEY ("sourceContractId") REFERENCES public."Contract"(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: ReceiptLog ReceiptLog_goodReceiptItemId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ReceiptLog"
    ADD CONSTRAINT "ReceiptLog_goodReceiptItemId_fkey" FOREIGN KEY ("goodReceiptItemId") REFERENCES public."GoodReceiptItem"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: ReceiptLog ReceiptLog_loggedById_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."ReceiptLog"
    ADD CONSTRAINT "ReceiptLog_loggedById_fkey" FOREIGN KEY ("loggedById") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: SanctionedIndividual SanctionedIndividual_sourceBlacklistId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."SanctionedIndividual"
    ADD CONSTRAINT "SanctionedIndividual_sourceBlacklistId_fkey" FOREIGN KEY ("sourceBlacklistId") REFERENCES public."BlacklistEntry"(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: TaxExemption TaxExemption_createdBy_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."TaxExemption"
    ADD CONSTRAINT "TaxExemption_createdBy_fkey" FOREIGN KEY ("createdBy") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: TaxExemption TaxExemption_taxTypeId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."TaxExemption"
    ADD CONSTRAINT "TaxExemption_taxTypeId_fkey" FOREIGN KEY ("taxTypeId") REFERENCES public."TaxType"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: UserRole UserRole_roleId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."UserRole"
    ADD CONSTRAINT "UserRole_roleId_fkey" FOREIGN KEY ("roleId") REFERENCES public."Role"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: UserRole UserRole_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."UserRole"
    ADD CONSTRAINT "UserRole_userId_fkey" FOREIGN KEY ("userId") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: User User_departmentId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."User"
    ADD CONSTRAINT "User_departmentId_fkey" FOREIGN KEY ("departmentId") REFERENCES public."Department"(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: VendorEvaluation VendorEvaluation_evaluatedById_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."VendorEvaluation"
    ADD CONSTRAINT "VendorEvaluation_evaluatedById_fkey" FOREIGN KEY ("evaluatedById") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: VendorEvaluation VendorEvaluation_procurementId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."VendorEvaluation"
    ADD CONSTRAINT "VendorEvaluation_procurementId_fkey" FOREIGN KEY ("procurementId") REFERENCES public."Procurement"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: VendorEvaluation VendorEvaluation_vendorId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."VendorEvaluation"
    ADD CONSTRAINT "VendorEvaluation_vendorId_fkey" FOREIGN KEY ("vendorId") REFERENCES public."Vendor"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: VendorIssue VendorIssue_assignedToId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."VendorIssue"
    ADD CONSTRAINT "VendorIssue_assignedToId_fkey" FOREIGN KEY ("assignedToId") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: VendorIssue VendorIssue_procurementId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."VendorIssue"
    ADD CONSTRAINT "VendorIssue_procurementId_fkey" FOREIGN KEY ("procurementId") REFERENCES public."Procurement"(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: VendorIssue VendorIssue_purchaseOrderId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."VendorIssue"
    ADD CONSTRAINT "VendorIssue_purchaseOrderId_fkey" FOREIGN KEY ("purchaseOrderId") REFERENCES public."PurchaseOrder"(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: VendorIssue VendorIssue_reportedById_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."VendorIssue"
    ADD CONSTRAINT "VendorIssue_reportedById_fkey" FOREIGN KEY ("reportedById") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: VendorIssue VendorIssue_vendorId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."VendorIssue"
    ADD CONSTRAINT "VendorIssue_vendorId_fkey" FOREIGN KEY ("vendorId") REFERENCES public."Vendor"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: VendorKpiEvaluation VendorKpiEvaluation_approvedById_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."VendorKpiEvaluation"
    ADD CONSTRAINT "VendorKpiEvaluation_approvedById_fkey" FOREIGN KEY ("approvedById") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: VendorKpiEvaluation VendorKpiEvaluation_evaluatedById_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."VendorKpiEvaluation"
    ADD CONSTRAINT "VendorKpiEvaluation_evaluatedById_fkey" FOREIGN KEY ("evaluatedById") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: VendorKpiEvaluation VendorKpiEvaluation_templateId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."VendorKpiEvaluation"
    ADD CONSTRAINT "VendorKpiEvaluation_templateId_fkey" FOREIGN KEY ("templateId") REFERENCES public."VendorKpiTemplate"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: VendorKpiEvaluation VendorKpiEvaluation_vendorId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."VendorKpiEvaluation"
    ADD CONSTRAINT "VendorKpiEvaluation_vendorId_fkey" FOREIGN KEY ("vendorId") REFERENCES public."Vendor"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: VendorKpiMetric VendorKpiMetric_recordedById_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."VendorKpiMetric"
    ADD CONSTRAINT "VendorKpiMetric_recordedById_fkey" FOREIGN KEY ("recordedById") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: VendorKpiMetric VendorKpiMetric_vendorId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."VendorKpiMetric"
    ADD CONSTRAINT "VendorKpiMetric_vendorId_fkey" FOREIGN KEY ("vendorId") REFERENCES public."Vendor"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: VendorKpiScore VendorKpiScore_evaluationId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."VendorKpiScore"
    ADD CONSTRAINT "VendorKpiScore_evaluationId_fkey" FOREIGN KEY ("evaluationId") REFERENCES public."VendorKpiEvaluation"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: VendorKpiScore VendorKpiScore_metricId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."VendorKpiScore"
    ADD CONSTRAINT "VendorKpiScore_metricId_fkey" FOREIGN KEY ("metricId") REFERENCES public."VendorKpiMetric"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: VendorKpiTemplate VendorKpiTemplate_createdById_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."VendorKpiTemplate"
    ADD CONSTRAINT "VendorKpiTemplate_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: VendorKpi VendorKpi_vendorId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."VendorKpi"
    ADD CONSTRAINT "VendorKpi_vendorId_fkey" FOREIGN KEY ("vendorId") REFERENCES public."Vendor"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: VendorOfferItem VendorOfferItem_correctedById_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."VendorOfferItem"
    ADD CONSTRAINT "VendorOfferItem_correctedById_fkey" FOREIGN KEY ("correctedById") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: VendorOfferItem VendorOfferItem_itemId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."VendorOfferItem"
    ADD CONSTRAINT "VendorOfferItem_itemId_fkey" FOREIGN KEY ("itemId") REFERENCES public."ProcurementItem"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: VendorOfferItem VendorOfferItem_offerId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."VendorOfferItem"
    ADD CONSTRAINT "VendorOfferItem_offerId_fkey" FOREIGN KEY ("offerId") REFERENCES public."VendorOffer"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: VendorOffer VendorOffer_procurementId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."VendorOffer"
    ADD CONSTRAINT "VendorOffer_procurementId_fkey" FOREIGN KEY ("procurementId") REFERENCES public."Procurement"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: VendorOffer VendorOffer_vendorId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."VendorOffer"
    ADD CONSTRAINT "VendorOffer_vendorId_fkey" FOREIGN KEY ("vendorId") REFERENCES public."Vendor"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: VendorPerformanceHistory VendorPerformanceHistory_vendorId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."VendorPerformanceHistory"
    ADD CONSTRAINT "VendorPerformanceHistory_vendorId_fkey" FOREIGN KEY ("vendorId") REFERENCES public."Vendor"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: VendorRequirementTemplate VendorRequirementTemplate_createdBy_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."VendorRequirementTemplate"
    ADD CONSTRAINT "VendorRequirementTemplate_createdBy_fkey" FOREIGN KEY ("createdBy") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: VendorRequirementTemplate VendorRequirementTemplate_templateId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."VendorRequirementTemplate"
    ADD CONSTRAINT "VendorRequirementTemplate_templateId_fkey" FOREIGN KEY ("templateId") REFERENCES public."ProcurementWorkflowTemplate"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: VendorVerificationStep VendorVerificationStep_vendorId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."VendorVerificationStep"
    ADD CONSTRAINT "VendorVerificationStep_vendorId_fkey" FOREIGN KEY ("vendorId") REFERENCES public."Vendor"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: VendorVerificationStep VendorVerificationStep_verifiedById_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."VendorVerificationStep"
    ADD CONSTRAINT "VendorVerificationStep_verifiedById_fkey" FOREIGN KEY ("verifiedById") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: Vendor Vendor_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public."Vendor"
    ADD CONSTRAINT "Vendor_userId_fkey" FOREIGN KEY ("userId") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: about_page_content about_page_content_updatedById_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public.about_page_content
    ADD CONSTRAINT "about_page_content_updatedById_fkey" FOREIGN KEY ("updatedById") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: audit_logs audit_logs_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public.audit_logs
    ADD CONSTRAINT "audit_logs_userId_fkey" FOREIGN KEY ("userId") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: document_template_versions document_template_versions_createdBy_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public.document_template_versions
    ADD CONSTRAINT "document_template_versions_createdBy_fkey" FOREIGN KEY ("createdBy") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: document_template_versions document_template_versions_templateId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public.document_template_versions
    ADD CONSTRAINT "document_template_versions_templateId_fkey" FOREIGN KEY ("templateId") REFERENCES public.document_templates(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: document_templates document_templates_approvedBy_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public.document_templates
    ADD CONSTRAINT "document_templates_approvedBy_fkey" FOREIGN KEY ("approvedBy") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: document_templates document_templates_createdBy_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public.document_templates
    ADD CONSTRAINT "document_templates_createdBy_fkey" FOREIGN KEY ("createdBy") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: document_templates document_templates_updatedBy_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public.document_templates
    ADD CONSTRAINT "document_templates_updatedBy_fkey" FOREIGN KEY ("updatedBy") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: generated_documents generated_documents_createdBy_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public.generated_documents
    ADD CONSTRAINT "generated_documents_createdBy_fkey" FOREIGN KEY ("createdBy") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: generated_documents generated_documents_templateId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public.generated_documents
    ADD CONSTRAINT "generated_documents_templateId_fkey" FOREIGN KEY ("templateId") REFERENCES public.document_templates(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: notifications notifications_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public.notifications
    ADD CONSTRAINT "notifications_userId_fkey" FOREIGN KEY ("userId") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: public_assets public_assets_parentId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public.public_assets
    ADD CONSTRAINT "public_assets_parentId_fkey" FOREIGN KEY ("parentId") REFERENCES public.public_assets(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: public_assets public_assets_uploadedById_fkey; Type: FK CONSTRAINT; Schema: public; Owner: avnadmin
--

ALTER TABLE ONLY public.public_assets
    ADD CONSTRAINT "public_assets_uploadedById_fkey" FOREIGN KEY ("uploadedById") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: SCHEMA public; Type: ACL; Schema: -; Owner: avnadmin
--

REVOKE USAGE ON SCHEMA public FROM PUBLIC;


--
-- Name: FUNCTION pg_stat_statements_reset(userid oid, dbid oid, queryid bigint); Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON FUNCTION public.pg_stat_statements_reset(userid oid, dbid oid, queryid bigint) TO avnadmin WITH GRANT OPTION;


--
-- PostgreSQL database dump complete
--

