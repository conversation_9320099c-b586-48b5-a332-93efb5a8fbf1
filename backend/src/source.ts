import { DataSource, DataSourceOptions } from 'typeorm';
import { SeederOptions } from 'typeorm-extension';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

export const dataSourceOptions: DataSourceOptions & SeederOptions = {
  type: 'postgres',
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT),
  username: process.env.DB_USER,
  password: process.env.DB_PASS,
  database: process.env.DB_NAME,
  schema: process.env.DB_SCHEMA || 'csrit_backend',

  // 🌩️ Cloud-optimized SSL configuration
  ssl: process.env.DB_SSL === 'true' ? {
    rejectUnauthorized: process.env.DB_SSL_REJECT_UNAUTHORIZED !== 'false',
  } : false,

  // 🚀 Performance optimizations for cloud
  extra: {
    // Connection pool settings (optimized for cloud)
    max: parseInt(process.env.DB_POOL_MAX) || 15,
    min: parseInt(process.env.DB_POOL_MIN) || 3,

    // Timeout settings (higher for cloud latency)
    acquireTimeoutMillis: 60000,
    idleTimeoutMillis: 300000,
    connectionTimeoutMillis: parseInt(process.env.DB_CONNECTION_TIMEOUT) || 10000,

    // Query timeout
    statement_timeout: parseInt(process.env.DB_QUERY_TIMEOUT) || 30000,
    query_timeout: parseInt(process.env.DB_QUERY_TIMEOUT) || 30000,

    // Cloud-specific optimizations
    keepAlive: true,
    keepAliveInitialDelayMillis: 0,
  },

  // 🔄 Migration settings
  migrations: [__dirname + '/migrations/*.js'],
  entities: [__dirname + '/**/*.entity.js'],
  migrationsRun: false, // Don't auto-run in cloud

  // 📊 Logging (adjust for cloud)
  logging: process.env.ENABLE_QUERY_LOGGING === 'true'
    ? ['query', 'error', 'warn', 'migration']
    : ['error', 'warn', 'migration'],

  maxQueryExecutionTime: parseInt(process.env.SLOW_QUERY_THRESHOLD) || 1000,

  // 🌱 Seeds
  seeds: ['seeds/**/*.ts'],
};

// Create and export DataSource instance
const dataSource = new DataSource(dataSourceOptions);

export default dataSource;
