import { DataSourceOptions } from 'typeorm';
import { SeederOptions } from 'typeorm-extension';

/**
 * 🔒 Production-Grade Database Configuration
 * Implements security, reliability, and performance best practices
 */
export const getProductionDatabaseConfig = (): DataSourceOptions & SeederOptions => {
  const isProduction = process.env.NODE_ENV === 'production';
  
  // 🚨 CRITICAL: Validate required environment variables
  const requiredEnvVars = [
    'DB_HOST', 'DB_PORT', 'DB_USER', 'DB_PASS', 'DB_NAME'
  ];
  
  for (const envVar of requiredEnvVars) {
    if (!process.env[envVar]) {
      throw new Error(`Missing required environment variable: ${envVar}`);
    }
  }

  return {
    type: 'postgres',
    host: process.env.DB_HOST,
    port: parseInt(process.env.DB_PORT),
    username: process.env.DB_USER,
    password: process.env.DB_PASS,
    database: process.env.DB_NAME,
    
    // 🔒 SECURITY CONFIGURATION
    ssl: isProduction ? {
      rejectUnauthorized: process.env.DB_SSL_REJECT_UNAUTHORIZED === 'true',
      ca: process.env.DB_SSL_CA, // SSL Certificate Authority
      cert: process.env.DB_SSL_CERT, // Client certificate
      key: process.env.DB_SSL_KEY, // Client private key
    } : false,
    
    // 🚨 CRITICAL PRODUCTION SETTINGS
    synchronize: false, // NEVER enable in production
    migrationsRun: isProduction, // Auto-run migrations in production
    migrations: ['dist/migrations/*.js'],
    entities: ['dist/**/*.entity{.ts,.js}'],
    
    // 🚀 PERFORMANCE & RELIABILITY CONFIGURATION
    extra: {
      // Connection Pool Settings
      max: parseInt(process.env.DB_POOL_MAX) || (isProduction ? 20 : 10),
      min: parseInt(process.env.DB_POOL_MIN) || (isProduction ? 5 : 2),
      
      // Timeout Configuration
      acquireTimeoutMillis: 60000, // 1 minute to acquire connection
      idleTimeoutMillis: 600000,   // 10 minutes idle timeout
      reapIntervalMillis: 1000,    // Check for idle connections every second
      createRetryIntervalMillis: 200, // Retry connection creation
      
      // Query Timeout Settings
      statement_timeout: parseInt(process.env.DB_QUERY_TIMEOUT) || 30000, // 30 seconds
      query_timeout: parseInt(process.env.DB_QUERY_TIMEOUT) || 30000,
      connectionTimeoutMillis: parseInt(process.env.DB_CONNECTION_TIMEOUT) || 5000,
      
      // PostgreSQL Specific Settings
      application_name: process.env.APP_NAME || 'CSRIT-Backend',
      
      // Connection Security
      sslmode: isProduction ? 'require' : 'prefer',
      
      // Performance Tuning
      shared_preload_libraries: 'pg_stat_statements',
      
      // Reliability Settings
      tcp_keepalives_idle: 600,     // 10 minutes
      tcp_keepalives_interval: 30,  // 30 seconds
      tcp_keepalives_count: 3,      // 3 retries
    },
    
    // 📊 MONITORING & LOGGING
    logging: isProduction 
      ? ['error', 'warn', 'migration', 'schema'] 
      : process.env.ENABLE_QUERY_LOGGING === 'true' 
        ? ['query', 'error', 'warn', 'migration', 'schema']
        : ['error', 'warn', 'migration', 'schema'],
    
    // Log slow queries (queries taking longer than threshold)
    maxQueryExecutionTime: parseInt(process.env.SLOW_QUERY_THRESHOLD) || 1000,
    
    // 💾 CACHING CONFIGURATION
    cache: process.env.REDIS_HOST ? {
      type: 'ioredis',
      options: {
        host: process.env.REDIS_HOST,
        port: parseInt(process.env.REDIS_PORT) || 6379,
        password: process.env.REDIS_PASSWORD,
        db: parseInt(process.env.REDIS_DB) || 0,
        
        // Redis Connection Settings
        connectTimeout: 10000,
        commandTimeout: 5000,
        retryDelayOnFailover: 100,
        maxRetriesPerRequest: 3,
        
        // Connection Pool
        family: 4,
        keepAlive: true,
        
        // Reliability
        lazyConnect: true,
        enableReadyCheck: true,
      },
      duration: parseInt(process.env.CACHE_TTL) || 300000, // 5 minutes
      ignoreErrors: true, // Don't fail if cache is unavailable
    } : false,
    
    // 🌱 SEEDING CONFIGURATION
    seeds: isProduction ? [] : ['dist/seeds/*.js'], // No seeding in production
    
    // 📈 ADDITIONAL PRODUCTION SETTINGS
    dropSchema: false, // Never drop schema
    
    // Entity Loading Strategy
    relationLoadStrategy: 'query', // Use separate queries for relations (better performance)
    
    // Connection Naming
    name: 'default',
    
    // Timezone Configuration
    timezone: process.env.DB_TIMEZONE || 'UTC',
  };
};

/**
 * 🔧 Database Health Check Configuration
 */
export const getDatabaseHealthCheckConfig = () => ({
  // Health check query
  healthCheckQuery: 'SELECT 1',
  
  // Health check timeout
  healthCheckTimeout: 5000,
  
  // Health check interval (for monitoring)
  healthCheckInterval: 30000, // 30 seconds
});

/**
 * 📊 Database Monitoring Configuration
 */
export const getDatabaseMonitoringConfig = () => ({
  // Enable pg_stat_statements for query monitoring
  enableStatStatements: true,
  
  // Slow query threshold
  slowQueryThreshold: parseInt(process.env.SLOW_QUERY_THRESHOLD) || 1000,
  
  // Connection monitoring
  connectionMonitoring: {
    maxConnections: parseInt(process.env.DB_POOL_MAX) || 20,
    warningThreshold: 0.8, // Warn at 80% capacity
    criticalThreshold: 0.95, // Critical at 95% capacity
  },
  
  // Performance monitoring
  performanceMonitoring: {
    cacheHitRatioThreshold: 0.95, // Warn if cache hit ratio < 95%
    indexUsageThreshold: 100, // Warn if sequential scans > 100
  },
});

/**
 * 🔐 Database Security Configuration
 */
export const getDatabaseSecurityConfig = () => ({
  // Row Level Security (if needed)
  enableRLS: process.env.ENABLE_ROW_LEVEL_SECURITY === 'true',
  
  // Audit logging
  auditLogging: {
    enabled: process.env.ENABLE_AUDIT_LOGGING === 'true',
    logConnections: true,
    logDisconnections: true,
    logStatements: ['ddl', 'mod'], // Log DDL and modification statements
  },
  
  // Connection security
  connectionSecurity: {
    requireSSL: process.env.NODE_ENV === 'production',
    allowedHosts: process.env.DB_ALLOWED_HOSTS?.split(',') || [],
    maxConnectionsPerUser: 10,
  },
  
  // Password security
  passwordSecurity: {
    minLength: 12,
    requireComplexity: true,
    rotationDays: 90,
  },
});

/**
 * 💾 Backup Configuration
 */
export const getBackupConfig = () => ({
  // Backup schedule
  schedule: {
    daily: '0 2 * * *', // 2 AM daily
    weekly: '0 3 * * 0', // 3 AM Sunday
    monthly: '0 4 1 * *', // 4 AM 1st of month
  },
  
  // Backup retention
  retention: {
    daily: 7,   // Keep 7 daily backups
    weekly: 4,  // Keep 4 weekly backups
    monthly: 12, // Keep 12 monthly backups
  },
  
  // Backup location
  location: process.env.BACKUP_LOCATION || '/var/backups/postgresql',
  
  // Compression
  compression: true,
  compressionLevel: 6,
  
  // Encryption
  encryption: {
    enabled: process.env.BACKUP_ENCRYPTION === 'true',
    key: process.env.BACKUP_ENCRYPTION_KEY,
  },
});

/**
 * 🚨 Disaster Recovery Configuration
 */
export const getDisasterRecoveryConfig = () => ({
  // Point-in-time recovery
  pointInTimeRecovery: {
    enabled: true,
    archiveMode: 'on',
    archiveCommand: process.env.WAL_ARCHIVE_COMMAND,
    walLevel: 'replica',
  },
  
  // Replication (if configured)
  replication: {
    enabled: process.env.ENABLE_REPLICATION === 'true',
    standbyServers: process.env.STANDBY_SERVERS?.split(',') || [],
    synchronousCommit: process.env.SYNCHRONOUS_COMMIT || 'on',
  },
  
  // Recovery testing
  recoveryTesting: {
    schedule: '0 5 * * 6', // 5 AM Saturday
    retentionDays: 30,
  },
});
