import { Injectable, Logger } from '@nestjs/common';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';
import { Cron, CronExpression } from '@nestjs/schedule';

export interface DatabaseMetrics {
  timestamp: Date;
  connectionCount: number;
  activeConnections: number;
  idleConnections: number;
  slowQueries: number;
  avgQueryTime: number;
  indexUsage: IndexUsageMetric[];
  tableStats: TableStatsMetric[];
  cacheHitRatio: number;
}

export interface IndexUsageMetric {
  tableName: string;
  indexName: string;
  indexScans: number;
  tupleReads: number;
  tuplesFetched: number;
}

export interface TableStatsMetric {
  tableName: string;
  totalSize: string;
  rowCount: number;
  seqScans: number;
  indexScans: number;
  insertCount: number;
  updateCount: number;
  deleteCount: number;
}

@Injectable()
export class DatabaseMonitorService {
  private readonly logger = new Logger(DatabaseMonitorService.name);

  constructor(
    @InjectDataSource()
    private dataSource: DataSource,
  ) {}

  // 📊 Real-time Database Metrics Collection
  async getDatabaseMetrics(): Promise<DatabaseMetrics> {
    try {
      const [
        connectionStats,
        slowQueries,
        avgQueryTime,
        indexUsage,
        tableStats,
        cacheHitRatio,
      ] = await Promise.all([
        this.getConnectionStats(),
        this.getSlowQueriesCount(),
        this.getAverageQueryTime(),
        this.getIndexUsageStats(),
        this.getTableStats(),
        this.getCacheHitRatio(),
      ]);

      return {
        timestamp: new Date(),
        connectionCount: connectionStats.total,
        activeConnections: connectionStats.active,
        idleConnections: connectionStats.idle,
        slowQueries,
        avgQueryTime,
        indexUsage,
        tableStats,
        cacheHitRatio,
      };
    } catch (error) {
      this.logger.error('Failed to collect database metrics', error);
      throw error;
    }
  }

  // 🔗 Connection Pool Monitoring
  private async getConnectionStats() {
    const result = await this.dataSource.query(`
      SELECT 
        COUNT(*) as total,
        COUNT(*) FILTER (WHERE state = 'active') as active,
        COUNT(*) FILTER (WHERE state = 'idle') as idle,
        COUNT(*) FILTER (WHERE state = 'idle in transaction') as idle_in_transaction
      FROM pg_stat_activity 
      WHERE datname = current_database()
    `);

    return result[0];
  }

  // 🐌 Slow Query Detection
  private async getSlowQueriesCount(): Promise<number> {
    // This requires pg_stat_statements extension
    try {
      const result = await this.dataSource.query(`
        SELECT COUNT(*) as slow_queries
        FROM pg_stat_statements 
        WHERE mean_exec_time > 1000  -- Queries slower than 1 second
      `);
      return parseInt(result[0]?.slow_queries || '0');
    } catch (error) {
      // pg_stat_statements not available
      return 0;
    }
  }

  // ⏱️ Average Query Time
  private async getAverageQueryTime(): Promise<number> {
    try {
      const result = await this.dataSource.query(`
        SELECT AVG(mean_exec_time) as avg_time
        FROM pg_stat_statements
      `);
      return parseFloat(result[0]?.avg_time || '0');
    } catch (error) {
      return 0;
    }
  }

  // 📈 Index Usage Statistics
  private async getIndexUsageStats(): Promise<IndexUsageMetric[]> {
    const result = await this.dataSource.query(`
      SELECT 
        schemaname,
        tablename,
        indexname,
        idx_scan as index_scans,
        idx_tup_read as tuple_reads,
        idx_tup_fetch as tuples_fetched
      FROM pg_stat_user_indexes 
      WHERE schemaname = 'public'
      ORDER BY idx_scan DESC
      LIMIT 20
    `);

    return result.map(row => ({
      tableName: row.tablename,
      indexName: row.indexname,
      indexScans: parseInt(row.index_scans),
      tupleReads: parseInt(row.tuple_reads),
      tuplesFetched: parseInt(row.tuples_fetched),
    }));
  }

  // 📊 Table Statistics
  private async getTableStats(): Promise<TableStatsMetric[]> {
    const result = await this.dataSource.query(`
      SELECT 
        schemaname,
        tablename,
        pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as total_size,
        n_tup_ins as insert_count,
        n_tup_upd as update_count,
        n_tup_del as delete_count,
        seq_scan as seq_scans,
        idx_scan as index_scans,
        n_live_tup as row_count
      FROM pg_stat_user_tables 
      WHERE schemaname = 'public'
      ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
    `);

    return result.map(row => ({
      tableName: row.tablename,
      totalSize: row.total_size,
      rowCount: parseInt(row.row_count || '0'),
      seqScans: parseInt(row.seq_scans || '0'),
      indexScans: parseInt(row.index_scans || '0'),
      insertCount: parseInt(row.insert_count || '0'),
      updateCount: parseInt(row.update_count || '0'),
      deleteCount: parseInt(row.delete_count || '0'),
    }));
  }

  // 💾 Cache Hit Ratio
  private async getCacheHitRatio(): Promise<number> {
    const result = await this.dataSource.query(`
      SELECT 
        ROUND(
          (sum(heap_blks_hit) / (sum(heap_blks_hit) + sum(heap_blks_read))) * 100, 
          2
        ) as cache_hit_ratio
      FROM pg_statio_user_tables
    `);

    return parseFloat(result[0]?.cache_hit_ratio || '0');
  }

  // 🚨 Performance Alerts
  async checkPerformanceAlerts(): Promise<string[]> {
    const alerts: string[] = [];
    const metrics = await this.getDatabaseMetrics();

    // Connection pool alerts
    if (metrics.activeConnections > 15) {
      alerts.push(`High active connections: ${metrics.activeConnections}/20`);
    }

    // Slow query alerts
    if (metrics.slowQueries > 10) {
      alerts.push(`High number of slow queries: ${metrics.slowQueries}`);
    }

    // Cache hit ratio alerts
    if (metrics.cacheHitRatio < 95) {
      alerts.push(`Low cache hit ratio: ${metrics.cacheHitRatio}%`);
    }

    // Table scan alerts (should use indexes)
    const problematicTables = metrics.tableStats.filter(
      table => table.seqScans > table.indexScans && table.seqScans > 100
    );

    if (problematicTables.length > 0) {
      alerts.push(
        `Tables with high sequential scans: ${problematicTables.map(t => t.tableName).join(', ')}`
      );
    }

    return alerts;
  }

  // 📊 Content Performance Metrics
  async getContentPerformanceMetrics() {
    const contentTables = ['berita', 'artikel', 'layanan', 'event'];
    const metrics = {};

    for (const table of contentTables) {
      const result = await this.dataSource.query(`
        SELECT 
          COUNT(*) as total_records,
          pg_size_pretty(pg_total_relation_size('${table}')) as table_size,
          (SELECT COUNT(*) FROM pg_stat_user_indexes WHERE tablename = '${table}') as index_count
        FROM ${table}
      `);

      metrics[table] = result[0];
    }

    return metrics;
  }

  // 💬 Chat System Performance Metrics
  async getChatPerformanceMetrics() {
    const result = await this.dataSource.query(`
      SELECT 
        (SELECT COUNT(*) FROM chat_rooms WHERE status = 'active') as active_rooms,
        (SELECT COUNT(*) FROM chat_messages WHERE created_at > NOW() - INTERVAL '1 hour') as messages_last_hour,
        (SELECT COUNT(*) FROM chat_users WHERE is_active = true) as active_users,
        (SELECT AVG(EXTRACT(EPOCH FROM (updated_at - created_at))) FROM chat_messages WHERE created_at > NOW() - INTERVAL '1 day') as avg_response_time
    `);

    return result[0];
  }

  // 🔄 Automated Monitoring (runs every 5 minutes)
  @Cron(CronExpression.EVERY_5_MINUTES)
  async performanceMonitoring() {
    try {
      const alerts = await this.checkPerformanceAlerts();
      
      if (alerts.length > 0) {
        this.logger.warn('Performance alerts detected:', alerts);
        // Here you could send notifications to Slack, email, etc.
      }

      // Log key metrics every 5 minutes
      const metrics = await this.getDatabaseMetrics();
      this.logger.log(`DB Metrics - Connections: ${metrics.activeConnections}/${metrics.connectionCount}, Cache Hit: ${metrics.cacheHitRatio}%, Avg Query: ${metrics.avgQueryTime}ms`);

    } catch (error) {
      this.logger.error('Performance monitoring failed', error);
    }
  }

  // 📈 Daily Performance Report
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async generateDailyReport() {
    try {
      const [dbMetrics, contentMetrics, chatMetrics] = await Promise.all([
        this.getDatabaseMetrics(),
        this.getContentPerformanceMetrics(),
        this.getChatPerformanceMetrics(),
      ]);

      const report = {
        date: new Date().toISOString().split('T')[0],
        database: dbMetrics,
        content: contentMetrics,
        chat: chatMetrics,
      };

      this.logger.log('Daily Performance Report Generated', JSON.stringify(report, null, 2));
      
      // Store report or send to monitoring service
      
    } catch (error) {
      this.logger.error('Failed to generate daily report', error);
    }
  }
}
