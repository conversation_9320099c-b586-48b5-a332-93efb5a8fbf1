import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddUserManagementIndexes1735000003000 implements MigrationInterface {
  name = 'AddUserManagementIndexes1735000003000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    console.log('🚀 Starting Migration 003: User Management Indexes');
    
    try {
      // 👤 USERS - Core User Management
      console.log('Creating users indexes...');
      
      // Role-based queries with status
      await queryRunner.query(`
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_role_status 
        ON users (roleId, status)
      `);
      
      // Active users only (partial index)
      await queryRunner.query(`
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_status_active 
        ON users (roleId) 
        WHERE status = 1
      `);
      
      // User lookup optimizations
      await queryRunner.query(`
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_created_at_desc 
        ON users (created_at DESC)
      `);

      // 🔐 USER ACTIVITY - Audit Trail Performance
      console.log('Creating user_activity indexes...');
      
      // User-specific activity queries
      await queryRunner.query(`
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_activity_username_created 
        ON user_activity (username, created_at DESC)
      `);
      
      // Route and action analysis
      await queryRunner.query(`
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_activity_route_action 
        ON user_activity (route, action)
      `);
      
      // Recent activity only (partial index for performance)
      await queryRunner.query(`
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_activity_recent 
        ON user_activity (created_at DESC) 
        WHERE created_at > CURRENT_DATE - INTERVAL '30 days'
      `);
      
      // IP-based analysis
      await queryRunner.query(`
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_activity_ip_created 
        ON user_activity (ip, created_at DESC)
      `);

      // 🎭 MASTER ROLE - Role Management
      console.log('Creating master_role indexes...');
      
      // Role name lookups
      await queryRunner.query(`
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_master_role_name 
        ON master_role (name)
      `);

      // 🔑 HAK AKSES - Access Rights
      console.log('Creating hak_akses indexes...');
      
      // Role-based access queries
      await queryRunner.query(`
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_hak_akses_role_menu 
        ON hak_akses (roleId, id_menu)
      `);
      
      // Menu-based access queries
      await queryRunner.query(`
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_hak_akses_menu 
        ON hak_akses (id_menu)
      `);

      // 📋 MENU - Navigation Performance
      console.log('Creating menu indexes...');
      
      // Menu hierarchy queries
      await queryRunner.query(`
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_menu_parent 
        ON menu (id_menu)
      `);
      
      // Menu type and position
      await queryRunner.query(`
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_menu_jenis_posisi 
        ON menu (jenis_menu, posisi)
      `);

      // 🎫 TICKETING SYSTEM - Support Performance
      console.log('Creating ticketing indexes...');
      
      // Ticket status and progress
      await queryRunner.query(`
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ticketing_progres_created 
        ON ticketing (progresId, created_at DESC)
      `);
      
      // Email-based ticket lookup
      await queryRunner.query(`
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ticketing_email_created 
        ON ticketing (email, created_at DESC)
      `);
      
      // Ticket number lookup (should be unique but adding index for performance)
      await queryRunner.query(`
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ticketing_number 
        ON ticketing (number)
      `);

      // 📊 TICKET HISTORY - Audit Trail
      console.log('Creating riwayat_ticket indexes...');
      
      // Ticket history queries
      await queryRunner.query(`
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_riwayat_ticket_ticket_created 
        ON riwayat_ticket (ticketId, created_at ASC)
      `);
      
      // Progress-based history
      await queryRunner.query(`
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_riwayat_ticket_progres 
        ON riwayat_ticket (progresId)
      `);

      console.log('✅ Migration 003 completed successfully');
      
    } catch (error) {
      console.error('❌ Migration 003 failed:', error);
      throw error;
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    console.log('🔄 Rolling back Migration 003: User Management Indexes');
    
    try {
      const indexesToDrop = [
        'idx_riwayat_ticket_progres',
        'idx_riwayat_ticket_ticket_created',
        'idx_ticketing_number',
        'idx_ticketing_email_created',
        'idx_ticketing_progres_created',
        'idx_menu_jenis_posisi',
        'idx_menu_parent',
        'idx_hak_akses_menu',
        'idx_hak_akses_role_menu',
        'idx_master_role_name',
        'idx_user_activity_ip_created',
        'idx_user_activity_recent',
        'idx_user_activity_route_action',
        'idx_user_activity_username_created',
        'idx_users_created_at_desc',
        'idx_users_status_active',
        'idx_users_role_status',
      ];

      for (const indexName of indexesToDrop) {
        await queryRunner.query(`DROP INDEX IF EXISTS ${indexName}`);
      }

      console.log('✅ Migration 003 rollback completed');
      
    } catch (error) {
      console.error('❌ Migration 003 rollback failed:', error);
      throw error;
    }
  }
}
