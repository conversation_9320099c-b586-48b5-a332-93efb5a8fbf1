import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddContentPerformanceIndexes1735000001000 implements MigrationInterface {
  name = 'AddContentPerformanceIndexes1735000001000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    console.log('🚀 Starting Migration 001: Content Performance Indexes');
    
    try {
      // 📰 BERITA (News) - Most Critical for Performance
      console.log('Creating berita indexes...');
      await queryRunner.query(`
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_berita_tgl_posting_desc 
        ON berita (tgl_posting DESC)
      `);
      
      await queryRunner.query(`
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_berita_kategori 
        ON berita (kategori)
      `);
      
      await queryRunner.query(`
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_berita_slug 
        ON berita (slug)
      `);

      // 📄 ARTIKEL (Articles)
      console.log('Creating artikel indexes...');
      await queryRunner.query(`
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_artikel_tgl_posting_desc 
        ON artikel (tgl_posting DESC)
      `);
      
      await queryRunner.query(`
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_artikel_kategori 
        ON artikel (kategori)
      `);
      
      await queryRunner.query(`
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_artikel_slug 
        ON artikel (slug)
      `);

      // 🛠️ LAYANAN (Services)
      console.log('Creating layanan indexes...');
      await queryRunner.query(`
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_layanan_tgl_posting_desc 
        ON layanan (tgl_posting DESC)
      `);
      
      await queryRunner.query(`
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_layanan_kategori 
        ON layanan (kategori)
      `);
      
      await queryRunner.query(`
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_layanan_slug 
        ON layanan (slug)
      `);

      // 📅 EVENT Management
      console.log('Creating event indexes...');
      await queryRunner.query(`
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_event_tgl_awal 
        ON event (tgl_awal)
      `);
      
      await queryRunner.query(`
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_event_created_at_desc 
        ON event (created_at DESC)
      `);

      // 🔒 SECURITY CONTENT
      console.log('Creating security content indexes...');
      await queryRunner.query(`
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_peringatan_keamanan_tgl_posting_desc 
        ON peringatan_keamanan (tgl_posting DESC)
      `);
      
      await queryRunner.query(`
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_panduan_pedoman_tanggal_desc 
        ON panduan_pedoman (tanggal DESC)
      `);

      console.log('✅ Migration 001 completed successfully');
      
    } catch (error) {
      console.error('❌ Migration 001 failed:', error);
      throw error;
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    console.log('🔄 Rolling back Migration 001: Content Performance Indexes');
    
    try {
      // Drop indexes in reverse order
      const indexesToDrop = [
        'idx_panduan_pedoman_tanggal_desc',
        'idx_peringatan_keamanan_tgl_posting_desc',
        'idx_event_created_at_desc',
        'idx_event_tgl_awal',
        'idx_layanan_slug',
        'idx_layanan_kategori',
        'idx_layanan_tgl_posting_desc',
        'idx_artikel_slug',
        'idx_artikel_kategori',
        'idx_artikel_tgl_posting_desc',
        'idx_berita_slug',
        'idx_berita_kategori',
        'idx_berita_tgl_posting_desc',
      ];

      for (const indexName of indexesToDrop) {
        await queryRunner.query(`DROP INDEX IF EXISTS ${indexName}`);
      }

      console.log('✅ Migration 001 rollback completed');
      
    } catch (error) {
      console.error('❌ Migration 001 rollback failed:', error);
      throw error;
    }
  }
}
