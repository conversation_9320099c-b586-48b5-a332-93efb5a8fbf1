import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddAdvancedPerformanceIndexes1735000004000 implements MigrationInterface {
  name = 'AddAdvancedPerformanceIndexes1735000004000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    console.log('🚀 Starting Migration 004: Advanced Performance Indexes');
    
    try {
      // 🔍 COMPOSITE INDEXES - Multi-column Query Optimization
      console.log('Creating composite indexes for content filtering...');
      
      // Content filtering combinations (category + date)
      await queryRunner.query(`
        CREATE INDEX IF NOT EXISTS idx_berita_kategori_tgl_posting 
        ON berita (kategori, tgl_posting DESC)
      `);
      
      await queryRunner.query(`
        CREATE INDEX IF NOT EXISTS idx_artikel_kategori_tgl_posting 
        ON artikel (kategori, tgl_posting DESC)
      `);
      
      await queryRunner.query(`
        CREATE INDEX IF NOT EXISTS idx_layanan_kategori_tgl_posting 
        ON layanan (kategori, tgl_posting DESC)
      `);

      // 💬 ADVANCED CHAT INDEXES
      console.log('Creating advanced chat system indexes...');
      
      // Complex chat message queries
      await queryRunner.query(`
        CREATE INDEX IF NOT EXISTS idx_chat_messages_room_sender_timestamp 
        ON chat_messages (chatRoomId, sender_type, timestamp DESC)
      `);
      
      // Message status with room filtering
      await queryRunner.query(`
        CREATE INDEX IF NOT EXISTS idx_chat_messages_room_status 
        ON chat_messages (chatRoomId, status)
      `);
      
      // Unread message tracking
      await queryRunner.query(`
        CREATE INDEX IF NOT EXISTS idx_chat_participants_unread 
        ON chat_room_participants (chatRoomId, last_read_message_id)
      `);

      // 📊 MONITORING AND REPORTING INDEXES
      console.log('Creating monitoring indexes...');
      
      // Monitoring by year and month
      await queryRunner.query(`
        CREATE INDEX IF NOT EXISTS idx_monitoring_tahun_bulan 
        ON monitoring (tahunId, bulan)
      `);
      
      // Monitoring by type and date
      await queryRunner.query(`
        CREATE INDEX IF NOT EXISTS idx_monitoring_jenis_created 
        ON monitoring (jenis, created_at DESC)
      `);

      // 🔐 SECURITY CONTENT ADVANCED INDEXES
      console.log('Creating security content advanced indexes...');
      
      // Security guidance by name and date
      await queryRunner.query(`
        CREATE INDEX IF NOT EXISTS idx_panduan_keamanan_nama_created 
        ON panduan_keamanan (nama, created_at DESC)
      `);

      // 📁 FILE MANAGEMENT INDEXES
      console.log('Creating file management indexes...');
      
      // RFC file category and date
      await queryRunner.query(`
        CREATE INDEX IF NOT EXISTS idx_rfc_file_kategori_tgl_posting 
        ON rfc_file (kategori, tgl_posting DESC)
      `);

      // 🎯 SPECIALIZED PARTIAL INDEXES
      console.log('Creating specialized partial indexes...');
      
      // Recent content only (last 6 months)
      await queryRunner.query(`
        CREATE INDEX IF NOT EXISTS idx_berita_recent 
        ON berita (tgl_posting DESC) 
        WHERE tgl_posting > CURRENT_DATE - INTERVAL '6 months'
      `);
      
      await queryRunner.query(`
        CREATE INDEX IF NOT EXISTS idx_artikel_recent 
        ON artikel (tgl_posting DESC) 
        WHERE tgl_posting > CURRENT_DATE - INTERVAL '6 months'
      `);

      // Active chat rooms with recent activity
      await queryRunner.query(`
        CREATE INDEX IF NOT EXISTS idx_chat_rooms_recent_active 
        ON chat_rooms (last_message_at DESC) 
        WHERE status = 'active' AND last_message_at > CURRENT_DATE - INTERVAL '7 days'
      `);

      // 📈 PERFORMANCE MONITORING INDEXES
      console.log('Creating performance monitoring indexes...');
      
      // User activity patterns
      await queryRunner.query(`
        CREATE INDEX IF NOT EXISTS idx_user_activity_action_created 
        ON user_activity (action, created_at DESC)
      `);
      
      // Route performance analysis
      await queryRunner.query(`
        CREATE INDEX IF NOT EXISTS idx_user_activity_route_created 
        ON user_activity (route, created_at DESC)
      `);

      console.log('✅ Migration 004 completed successfully');
      
    } catch (error) {
      console.error('❌ Migration 004 failed:', error);
      throw error;
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    console.log('🔄 Rolling back Migration 004: Advanced Performance Indexes');
    
    try {
      const indexesToDrop = [
        'idx_user_activity_route_created',
        'idx_user_activity_action_created',
        'idx_chat_rooms_recent_active',
        'idx_artikel_recent',
        'idx_berita_recent',
        'idx_rfc_file_kategori_tgl_posting',
        'idx_panduan_keamanan_nama_created',
        'idx_monitoring_jenis_created',
        'idx_monitoring_tahun_bulan',
        'idx_chat_participants_unread',
        'idx_chat_messages_room_status',
        'idx_chat_messages_room_sender_timestamp',
        'idx_layanan_kategori_tgl_posting',
        'idx_artikel_kategori_tgl_posting',
        'idx_berita_kategori_tgl_posting',
      ];

      for (const indexName of indexesToDrop) {
        await queryRunner.query(`DROP INDEX IF EXISTS ${indexName}`);
      }

      console.log('✅ Migration 004 rollback completed');
      
    } catch (error) {
      console.error('❌ Migration 004 rollback failed:', error);
      throw error;
    }
  }
}
